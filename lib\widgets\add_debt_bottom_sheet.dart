import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/customer.dart';
import '../models/debt.dart';

import '../providers/debt_provider.dart';

import '../providers/card_type_provider.dart';
import '../providers/card_inventory_provider.dart';
import '../providers/form_data_provider.dart';
import '../providers/notification_provider.dart';
import '../database/database_helper.dart';

import '../widgets/animated_checkmark_widget.dart';
import '../widgets/arabic_month_date_picker.dart';
import '../screens/add_customer_screen.dart';

class AddDebtBottomSheet extends StatefulWidget {
  const AddDebtBottomSheet({super.key, this.customer, this.defaultDirection});
  final Customer? customer;
  final DebtDirection? defaultDirection;

  @override
  State<AddDebtBottomSheet> createState() => _AddDebtBottomSheetState();
}

class _AddDebtBottomSheetState extends State<AddDebtBottomSheet>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _customerNameController = TextEditingController();
  final _cardTypeController = TextEditingController();
  final _quantityController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  final _newCardTypeController = TextEditingController();
  final _cardTypeNotesController = TextEditingController();

  // Quick values are now managed by FormDataProvider

  final _newQuantityController = TextEditingController();
  final _newAmountController = TextEditingController();
  final _newNoteController = TextEditingController();

  // Quick notes are now managed by FormDataProvider

  Customer? _selectedCustomer;
  String? _selectedCardType;
  final List<Map<String, dynamic>> _selectedCardTypes =
      []; // قائمة الكارتات المحددة
  final List<String> _tempSelectedCardTypes =
      []; // قائمة مؤقتة للاختيار المتعدد في النافذة
  DateTime _entryDate = DateTime.now();
  DateTime _dueDate = () {
    final now = DateTime.now();
    // إضافة شهر كامل بدلاً من 7 أيام
    final future = DateTime(now.year, now.month + 1, now.day);
    return DateTime(future.year, future.month, future.day, 23, 59);
  }();
  bool _isLoading = false;
  bool _isMultipleCardMode = false; // وضع الكارتات المتعددة

  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.fastOutSlowIn,
    );

    _animationController.forward();

    if (widget.customer != null) {
      _selectedCustomer = widget.customer;
      _customerNameController.text = widget.customer!.name;
    }

    _loadFormData();

    // Initialize card type after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cardTypeProvider = Provider.of<CardTypeProvider>(
        context,
        listen: false,
      );
      cardTypeProvider.loadCustomCardTypes().then((_) {
        if (mounted) {
          setState(() {
            // Don't set a default card type - user must choose
            if (_selectedCardType != null &&
                !cardTypeProvider.allCardTypes.any(
                  (opt) => opt.id == _selectedCardType,
                )) {
              // If the selected type is not found, clear it
              _selectedCardType = null;
            }
          });
        }
      });
    });
  }

  void _loadFormData() async {
    final formDataProvider = Provider.of<FormDataProvider>(
      context,
      listen: false,
    );

    // تحميل البيانات بشكل غير متزامن لتسريع فتح النافذة
    if (!formDataProvider.isLoaded) {
      // تحميل في الخلفية بدون انتظار
      unawaited(formDataProvider.loadFormData());
    }

    if (mounted) {
      setState(() {
        // دائماً ابدأ بكمية 1 للديون الجديدة
        _quantityController.text = '1';
        // استخدام القيم المحفوظة إذا كانت متاحة، وإلا استخدام قيم افتراضية
        _amountController.text =
            formDataProvider.isLoaded ? formDataProvider.lastAmount : '';
        _notesController.text =
            formDataProvider.isLoaded ? formDataProvider.lastNotes : '';
      });

      // إذا لم تكن البيانات محملة، استمع للتحديثات
      if (!formDataProvider.isLoaded) {
        formDataProvider.addListener(_onFormDataLoaded);
      }
    }
  }

  void _onFormDataLoaded() {
    final formDataProvider = Provider.of<FormDataProvider>(
      context,
      listen: false,
    );

    if (mounted && formDataProvider.isLoaded) {
      formDataProvider.removeListener(_onFormDataLoaded);
      setState(() {
        // تحديث القيم بعد التحميل
        if (_amountController.text.isEmpty) {
          _amountController.text = formDataProvider.lastAmount;
        }
        if (_notesController.text.isEmpty) {
          _notesController.text = formDataProvider.lastNotes;
        }
      });
    }
  }

  // تنفيذ العمليات الثانوية في الخلفية لتحسين الاستجابة
  Future<void> _performBackgroundOperations(DebtProvider debtProvider) async {
    try {
      // حفظ بيانات النموذج للمرة القادمة
      _saveFormData();

      // تحديث قائمة الديون إذا كنا نعرض ديون هذا العميل
      if (debtProvider.currentCustomerId == _selectedCustomer?.id) {
        await debtProvider.refreshCurrentCustomerDebts();
      }

      // عرض رسالة تنبيه البيع في أعلى الشاشة
      _showSaleNotification();
    } catch (e) {
      debugPrint('خطأ في العمليات الخلفية: $e');
    }
  }

  // بناء حقل حديث ومنسق مع أزرار
  Widget _buildModernField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required Color iconColor,
    required VoidCallback onTap,
    required bool hasValue,
    required bool isRequired,
    String? displayText,
    bool showDeleteButton = false,
    VoidCallback? onDelete,
  }) {
    return Row(
      children: [
        // زر الحذف (نفس تصميم الكارتات المتعددة)
        if (showDeleteButton) ...[
          Container(
            margin: const EdgeInsets.only(top: 28), // محاذاة مع الحقل
            child: Container(
              width: 28,
              height: 28,
              decoration: BoxDecoration(
                color: Colors.red.shade500,
                borderRadius: BorderRadius.circular(14),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: onDelete,
                  borderRadius: BorderRadius.circular(14),
                  child: const Icon(
                    Icons.delete_outline,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],

        // الحقل الرئيسي مع العنوان المتحرك
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان المتحرك - يظهر فوق الحقل عند الملء
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                height: hasValue ? 24 : 0,
                child: hasValue
                    ? Row(
                        children: [
                          Icon(
                            icon,
                            size: 14,
                            color: iconColor,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            label,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: iconColor,
                            ),
                          ),
                          if (isRequired) ...[
                            const SizedBox(width: 4),
                            Text(
                              '*',
                              style: TextStyle(
                                color: Colors.red.shade600,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ],
                      )
                    : const SizedBox.shrink(),
              ),

              // الحقل نفسه
              GestureDetector(
                onTap: onTap,
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color:
                        hasValue ? Colors.grey.shade50 : Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: hasValue
                          ? Colors.grey.shade400
                          : Colors.grey.shade300,
                      width: hasValue ? 1.5 : 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      // العنوان داخل الحقل عندما يكون فارغ
                      if (!hasValue) ...[
                        Icon(
                          icon,
                          size: 16,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          label,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey.shade500,
                          ),
                        ),
                        if (isRequired) ...[
                          const SizedBox(width: 4),
                          Text(
                            '*',
                            style: TextStyle(
                              color: Colors.red.shade400,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                        const Spacer(),
                      ],

                      // المحتوى عندما يكون ممتلئ
                      if (hasValue) ...[
                        Expanded(
                          child: Text(
                            displayText ??
                                (controller.text.isEmpty
                                    ? 'اضغط للاختيار'
                                    : controller.text),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey.shade800,
                            ),
                          ),
                        ),
                      ],

                      // الأيقونة الجانبية
                      Icon(
                        hasValue ? Icons.check_circle : Icons.arrow_forward_ios,
                        size: 18,
                        color: hasValue
                            ? Colors.green.shade600
                            : Colors.grey.shade400,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // بناء زر الحفظ الحديث
  Widget _buildModernSaveButton() {
    final isComplete = isFormComplete();

    return Container(
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: _isLoading
              ? [Colors.grey.shade300, Colors.grey.shade400]
              : isComplete
                  ? [Colors.blue.shade600, Colors.blue.shade700]
                  : [Colors.grey.shade200, Colors.grey.shade300],
        ),
        boxShadow: isComplete && !_isLoading
            ? [
                BoxShadow(
                  color: Colors.blue.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _isLoading ? null : (isComplete ? _saveDebt : null),
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (_isLoading) ...[
                  SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(Colors.grey.shade600),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'جاري الحفظ...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ] else ...[
                  Icon(
                    isComplete ? Icons.save_rounded : Icons.edit_note_rounded,
                    color: isComplete ? Colors.white : Colors.grey.shade500,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    isComplete ? 'إضافة الدين' : 'أكمل البيانات',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isComplete ? Colors.white : Colors.grey.shade500,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _saveFormData() {
    if (!mounted) return;
    try {
      final formDataProvider = Provider.of<FormDataProvider>(
        context,
        listen: false,
      );
      formDataProvider.saveFormData(
        quantity: _quantityController.text,
        amount: _amountController.text,
        notes: _notesController.text,
      );
    } catch (e) {
      // Ignore errors during dispose
    }
  }

  @override
  void dispose() {
    // Save values before disposing - but safely
    if (mounted) {
      try {
        _saveFormData();
      } catch (e) {
        // Ignore errors during dispose
      }
    }

    _animationController.dispose();
    _customerNameController.dispose();
    _cardTypeController.dispose();
    _quantityController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    _newCardTypeController.dispose();
    _cardTypeNotesController.dispose();
    _newQuantityController.dispose();
    _newAmountController.dispose();
    _newNoteController.dispose();
    super.dispose();
  }

  Future<void> _saveDebt() async {
    // الحصول على providers في بداية الدالة قبل أي عمليات غير متزامنة
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    final cardInventoryProvider = Provider.of<CardInventoryProvider>(
      context,
      listen: false,
    );
    final cardTypeProvider = Provider.of<CardTypeProvider>(
      context,
      listen: false,
    );

    if (!_formKey.currentState!.validate()) return;

    if (_selectedCustomer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار عميل أولاً'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // التحقق من الكارتات
    if (_selectedCardTypes.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إضافة كارت واحد على الأقل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // التحقق من السقف الائتماني
    if (!await _checkCreditLimit()) {
      return;
    }

    // الحفظ المباشر بدون نافذة مراجعة

    setState(() {
      _isLoading = true;
    });

    try {
      final now = DateTime.now();

      // Debug: طباعة حالة المتغيرات
      debugPrint(
        'DEBUG: _selectedCardTypes.length = ${_selectedCardTypes.length}',
      );
      debugPrint('DEBUG: _isMultipleCardMode = $_isMultipleCardMode');
      debugPrint('DEBUG: _selectedCardTypes = $_selectedCardTypes');

      // الآن نعتمد فقط على نظام الكارتات المتعددة
      if (_selectedCardTypes.isNotEmpty) {
        if (_isMultipleCardMode) {
          // وضع الدمج: دمج جميع الكارتات في بطاقة دين واحدة
          debugPrint('DEBUG: دخول وضع الدمج');
          double totalAmount = 0;
          int totalQuantity = 0;
          final List<String> cardTypeNames = [];

          // حساب الإجمالي وجمع أسماء الكارتات
          for (final cardData in _selectedCardTypes) {
            totalAmount += cardData['quantity'] * cardData['price'];
            totalQuantity += cardData['quantity'] as int;

            String actualCardTypeName = cardData['id'];
            if (cardData['id'].startsWith('custom_')) {
              final cardTypeId = int.parse(
                cardData['id'].replaceFirst('custom_', ''),
              );
              final customCardType =
                  cardTypeProvider.customCardTypes.firstWhere(
                (ct) => ct.id == cardTypeId,
                orElse: () => throw Exception('Card type not found'),
              );
              actualCardTypeName = customCardType.displayName;
            }
            cardTypeNames.add('${cardData['name']} (${cardData['quantity']})');

            // استقطاع من المخزون
            if (actualCardTypeName != 'نقدي' &&
                actualCardTypeName.toLowerCase() != 'cash') {
              final currentStock = cardInventoryProvider.getStockQuantity(
                actualCardTypeName,
              );

              if (currentStock >= cardData['quantity']) {
                await cardInventoryProvider.deductStock(
                  actualCardTypeName,
                  cardData['quantity'],
                );
              }
            }
          }

          // إنشاء بطاقة دين واحدة مدموجة
          final debt = Debt(
            customerId: _selectedCustomer!.id!,
            itemName: 'كارتات متعددة',
            quantity: totalQuantity,
            amount: totalAmount,
            cardType: 'متعدد: ${cardTypeNames.join(', ')}',
            notes: _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
            entryDate: _entryDate,
            dueDate: _dueDate,
            createdAt: now,
            updatedAt: now,
            direction: widget.defaultDirection ?? DebtDirection.customerOwesMe,
          );

          debugPrint('DEBUG: تم إنشاء بطاقة دين مدموجة واحدة');
          await debtProvider.addDebt(debt);
        } else {
          // وضع منفصل: حفظ كل كارت في بطاقة دين منفصلة
          debugPrint(
            'DEBUG: دخول وضع منفصل - سيتم إنشاء ${_selectedCardTypes.length} بطاقة',
          );
          for (final cardData in _selectedCardTypes) {
            String actualCardTypeName = cardData['id'];

            // إذا كان نوع مخصص، احصل على الاسم الفعلي
            if (cardData['id'].startsWith('custom_')) {
              final cardTypeId = int.parse(
                cardData['id'].replaceFirst('custom_', ''),
              );
              final customCardType =
                  cardTypeProvider.customCardTypes.firstWhere(
                (ct) => ct.id == cardTypeId,
                orElse: () => throw Exception('Card type not found'),
              );
              actualCardTypeName = customCardType.displayName;
            }

            final debt = Debt(
              customerId: _selectedCustomer!.id!,
              itemName: 'عنصر',
              quantity: cardData['quantity'],
              amount: cardData['quantity'] * cardData['price'],
              cardType: cardData['id'],
              notes: _notesController.text.trim().isEmpty
                  ? null
                  : _notesController.text.trim(),
              entryDate: _entryDate,
              dueDate: _dueDate,
              createdAt: now,
              updatedAt: now,
              direction:
                  widget.defaultDirection ?? DebtDirection.customerOwesMe,
            );

            // استقطاع ذكي من المخزون
            if (actualCardTypeName != 'نقدي' &&
                actualCardTypeName.toLowerCase() != 'cash') {
              final currentStock = cardInventoryProvider.getStockQuantity(
                actualCardTypeName,
              );

              if (currentStock >= cardData['quantity']) {
                await cardInventoryProvider.deductStock(
                  actualCardTypeName,
                  cardData['quantity'],
                );
              }
            }

            // حفظ الدين
            await debtProvider.addDebt(debt);
          }
        }
      }

      if (mounted) {
        // إغلاق النافذة فوراً لتحسين الاستجابة
        Navigator.of(context).pop();

        // عرض علامة صح متحركة مع تأثير جميل
        _showAnimatedCheckmark(context);
      }

      // تنفيذ العمليات الثانوية في الخلفية بدون انتظار
      unawaited(_performBackgroundOperations(debtProvider));
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // عرض رسالة تنبيه البيع في أعلى الشاشة
  void _showSaleNotification() {
    // التحقق من إعدادات التنبيهات أولاً
    final notificationProvider = Provider.of<NotificationProvider>(
      context,
      listen: false,
    );

    // إذا كانت تنبيهات البيع معطلة، لا تعرض شيء
    if (!notificationProvider.settings.salesNotificationsEnabled) {
      return;
    }

    final cardInventoryProvider = Provider.of<CardInventoryProvider>(
      context,
      listen: false,
    );
    final cardTypeProvider = Provider.of<CardTypeProvider>(
      context,
      listen: false,
    );

    // إنشاء رسالة التنبيه حسب نوع البيع
    String notificationMessage = '';

    if (_isMultipleCardMode && _selectedCardTypes.length > 1) {
      // بيع متعدد مدموج
      final totalQuantity = _selectedCardTypes.fold<int>(
        0,
        (sum, card) => sum + (card['quantity'] as int),
      );
      notificationMessage = '🎉 تم بيع $totalQuantity كارت متنوع بنجاح!';

      // إضافة تفاصيل كل كارت
      for (final cardData in _selectedCardTypes) {
        String actualCardTypeName = cardData['id'];

        // إذا كان نوع مخصص، احصل على الاسم الفعلي
        if (cardData['id'].startsWith('custom_')) {
          final cardTypeId = int.parse(
            cardData['id'].replaceFirst('custom_', ''),
          );
          final customCardType = cardTypeProvider.customCardTypes.firstWhere(
            (ct) => ct.id == cardTypeId,
            orElse: () => throw Exception('Card type not found'),
          );
          actualCardTypeName = customCardType.displayName;
        }

        final remainingStock = cardInventoryProvider.getStockQuantity(
          actualCardTypeName,
        );
        notificationMessage +=
            '\n• ${cardData['name']}: ${cardData['quantity']} (متبقي: $remainingStock)';
      }
    } else {
      // بيع منفصل أو كارت واحد
      for (final cardData in _selectedCardTypes) {
        String actualCardTypeName = cardData['id'];

        // إذا كان نوع مخصص، احصل على الاسم الفعلي
        if (cardData['id'].startsWith('custom_')) {
          final cardTypeId = int.parse(
            cardData['id'].replaceFirst('custom_', ''),
          );
          final customCardType = cardTypeProvider.customCardTypes.firstWhere(
            (ct) => ct.id == cardTypeId,
            orElse: () => throw Exception('Card type not found'),
          );
          actualCardTypeName = customCardType.displayName;
        }

        final remainingStock = cardInventoryProvider.getStockQuantity(
          actualCardTypeName,
        );
        notificationMessage +=
            '🎉 تم بيع ${cardData['quantity']} ${cardData['name']}\n';
        notificationMessage += '📦 متبقي في المخزون: $remainingStock\n';
      }
    }

    // عرض التنبيه في أعلى الشاشة باستخدام Overlay
    _showTopNotification(notificationMessage);
  }

  // عرض رسالة تنبيه في أعلى الشاشة
  void _showTopNotification(String message) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 10, // أسفل شريط الحالة مباشرة
        left: 16,
        right: 16,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade600, Colors.green.shade700],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    message,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                      height: 1.3,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () => overlayEntry.remove(),
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // إزالة الرسالة تلقائياً حسب إعدادات التنبيهات
    final notificationProvider = Provider.of<NotificationProvider>(
      context,
      listen: false,
    );
    final duration = notificationProvider.settings.notificationDuration;

    Timer(Duration(seconds: duration), () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  // إغلاق قائمة أنواع الكروت المنسدلة (لم تعد مستخدمة)
  void _closeCardTypeDropdown() {
    // لا حاجة لهذه الدالة بعد الآن
  }

  // إضافة كارت محدد إلى القائمة فوراً
  void _addSelectedCardToList(String cardId, String cardName, double price) {
    // الكمية الافتراضية دائماً 1 للكارتات الجديدة
    const int quantity = 1;

    // تحميل السعر المحفوظ من FormDataProvider
    final formDataProvider = Provider.of<FormDataProvider>(
      context,
      listen: false,
    );
    final savedPrice = formDataProvider.getCardTypePrice(cardName);
    final finalPrice =
        savedPrice ?? price; // استخدام السعر المحفوظ أو السعر المرسل

    // البحث عن كارت موجود من نفس النوع
    final existingIndex = _selectedCardTypes.indexWhere(
      (card) => card['id'] == cardId,
    );

    if (existingIndex != -1) {
      // استبدال الكارت الموجود بكمية 1
      _selectedCardTypes[existingIndex] = {
        'id': cardId,
        'name': cardName,
        'quantity': quantity, // دائماً 1
        'price': finalPrice,
      };
    } else {
      // إضافة كارت جديد بكمية 1
      _selectedCardTypes.add({
        'id': cardId,
        'name': cardName,
        'quantity': quantity, // دائماً 1
        'price': finalPrice,
      });
    }

    // إعادة تعيين حقل الكمية إلى 1
    setState(() {
      _quantityController.text = '1';
    });

    // رسالة نجاح سريعة
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('✅ تم إضافة $cardName (كمية: 1)'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 1),
        ),
      );
    }
  }

  // قسم دمج الكارتات الجديد - مبسط واحترافي
  Widget _buildMergeCardsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.merge_type,
              color: Colors.blue.shade600,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _selectedCardTypes.length > 1 ? 'دمج الكارتات' : 'نوع الحفظ',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
                Text(
                  _selectedCardTypes.length > 1
                      ? (_isMultipleCardMode
                          ? 'سيتم دمج جميع الكارتات في بطاقة دين واحدة'
                          : 'كل كارت سيحفظ في بطاقة دين منفصلة')
                      : 'كارت واحد محدد',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
          // زر الدمج الاحترافي (يظهر فقط عند وجود أكثر من كارت)
          if (_selectedCardTypes.length > 1)
            GestureDetector(
              onTap: () {
                setState(() {
                  _isMultipleCardMode = !_isMultipleCardMode;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: _isMultipleCardMode
                        ? [Colors.green.shade500, Colors.green.shade600]
                        : [Colors.grey.shade300, Colors.grey.shade400],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: (_isMultipleCardMode ? Colors.green : Colors.grey)
                          .withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _isMultipleCardMode ? Icons.merge_type : Icons.call_split,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      _isMultipleCardMode ? 'دمج' : 'منفصل',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  // تعديل سعر الكارت
  void _editCardPrice(int index, Map<String, dynamic> card) {
    final TextEditingController priceController = TextEditingController();
    priceController.text = card['price'].toString();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.edit, color: Colors.blue.shade600, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تعديل السعر',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  Text(
                    card['name'],
                    style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'السعر الحالي: ${card['price'].toStringAsFixed(0)} د ع',
              style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: TextField(
                controller: priceController,
                keyboardType: TextInputType.number,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
                decoration: InputDecoration(
                  labelText: 'السعر الجديد',
                  labelStyle: TextStyle(color: Colors.grey.shade600),
                  suffixText: 'د ع',
                  suffixStyle: TextStyle(
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                  floatingLabelBehavior: FloatingLabelBehavior.auto,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: TextStyle(color: Colors.grey.shade600)),
          ),
          ElevatedButton(
            onPressed: () async {
              final newPrice = double.tryParse(priceController.text);
              if (newPrice != null && newPrice > 0) {
                // الحصول على FormDataProvider و context قبل العمليات غير المتزامنة
                final formDataProvider = Provider.of<FormDataProvider>(
                  context,
                  listen: false,
                );
                final savedContext = context;

                // تحديث السعر في القائمة المحلية
                setState(() {
                  _selectedCardTypes[index]['price'] = newPrice;
                });

                // حفظ السعر بشكل دائم في FormDataProvider
                try {
                  await formDataProvider.setCardTypePrice(
                    card['name'],
                    newPrice,
                  );

                  if (mounted) {
                    Navigator.pop(savedContext);

                    // رسالة تأكيد
                    ScaffoldMessenger.of(savedContext).showSnackBar(
                      SnackBar(
                        content: Row(
                          children: [
                            const Icon(
                              Icons.check_circle,
                              color: Colors.white,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'تم حفظ سعر ${card['name']} بنجاح',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        backgroundColor: Colors.green.shade600,
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        margin: const EdgeInsets.all(16),
                        duration: const Duration(seconds: 3),
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    Navigator.pop(savedContext);
                    ScaffoldMessenger.of(savedContext).showSnackBar(
                      SnackBar(
                        content: Text('خطأ في حفظ السعر: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('يرجى إدخال سعر صحيح'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  // عرض الكارتات المحددة - بدون حواف خارجية رئيسية
  Widget _buildSelectedCardsDisplay() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header مع بطاقة أنيقة
        Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.grey.shade300, width: 0.8),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.15),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.blue.shade50,
                  Colors.blue.shade100.withValues(alpha: 0.3),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade600,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.layers_rounded,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'الكارتات المحددة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.blue.shade600, Colors.blue.shade700],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    '${_selectedCardTypes.length} كارت',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // قائمة الكارتات مع تصميم محسن - الكارتات تحتفظ بحوافها
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 12),
          child: Column(
            children: [
              ...List.generate(_selectedCardTypes.length, (index) {
                final card = _selectedCardTypes[index];
                return _buildElegantCardItem(card, index);
              }),

              const SizedBox(height: 12),

              // إجمالي احترافي - مخفي
              // _buildElegantTotal(),
            ],
          ),
        ),
      ],
    );
  }

  // دالة لتنسيق الأرقام مع فاصل الآلاف
  String _formatNumber(int number) {
    return number.toString().replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        );
  }

  // دالة للحصول على لون الكارت حسب النوع
  Color _getCardTypeColor(String cardTypeName) {
    switch (cardTypeName.toLowerCase()) {
      case 'زين':
        return Colors.purple;
      case 'آسيا':
        return Colors.red;
      case 'أبو الستة':
        return Colors.grey;
      case 'أبو العشرة':
        return Colors.cyan;
      default:
        return Colors.blue;
    }
  }

  // بطاقة كارت أنيقة ومحسنة - تصميم جديد مطابق للصورة
  Widget _buildElegantCardItem(Map<String, dynamic> card, int index) {
    // الحصول على لون الكارت
    final cardColor = _getCardTypeColor(card['name']);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300, width: 0.8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header الكارت مع اسم الكارت وزر الحذف
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // زر الحذف الأحمر
                Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: Colors.red.shade500,
                    borderRadius: BorderRadius.circular(14),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => _showDeleteConfirmation(index),
                      borderRadius: BorderRadius.circular(14),
                      child: const Icon(
                        Icons.delete_outline,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // اسم الكارت في المنتصف
                Expanded(
                  child: Text(
                    card['name'],
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                const SizedBox(width: 16),

                // أيقونة الكارت ثلاثية الأبعاد والواقعية
                Container(
                  width: 26,
                  height: 26,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        cardColor.withValues(alpha: 0.8),
                        cardColor,
                        cardColor.withValues(alpha: 1.2),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: cardColor.withValues(alpha: 0.4),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                        spreadRadius: 1,
                      ),
                      BoxShadow(
                        color: Colors.white.withValues(alpha: 0.8),
                        blurRadius: 1,
                        offset: const Offset(-1, -1),
                      ),
                    ],
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Stack(
                    children: [
                      // خلفية الكارت
                      Positioned.fill(
                        child: Container(
                          margin: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.white.withValues(alpha: 0.3),
                                Colors.white.withValues(alpha: 0.1),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ),
                      // الأيقونة
                      Center(
                        child: Icon(
                          Icons.credit_card_rounded,
                          color: Colors.white,
                          size: 13,
                          shadows: [
                            Shadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              offset: const Offset(1, 1),
                              blurRadius: 1,
                            ),
                          ],
                        ),
                      ),
                      // لمعة علوية
                      Positioned(
                        top: 2,
                        left: 2,
                        right: 2,
                        child: Container(
                          height: 8,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.white.withValues(alpha: 0.6),
                                Colors.white.withValues(alpha: 0.0),
                              ],
                            ),
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(8),
                              topRight: Radius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // قسم السعر والكمية
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                // قسم الكمية (في المكان الأول)
                Expanded(
                  child: Column(
                    children: [
                      // أيقونة الكمية (نفس تصميم السعر)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade100,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Icon(
                              Icons.format_list_numbered,
                              color: Colors.blue.shade600,
                              size: 16,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'الكمية',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 4),
                          const Icon(
                            Icons.numbers,
                            color: Colors.blue,
                            size: 16,
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // قيمة الكمية مع إمكانية التعديل (نفس تصميم السعر)
                      Column(
                        children: [
                          Row(
                            children: [
                              // زر النقصان
                              Container(
                                width: 28,
                                height: 28,
                                decoration: BoxDecoration(
                                  color: (card['quantity'] as int) > 1
                                      ? Colors.red.shade100
                                      : Colors.grey.shade200,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: (card['quantity'] as int) > 1
                                        ? () {
                                            setState(() {
                                              card['quantity'] =
                                                  (card['quantity'] as int) - 1;
                                            });
                                          }
                                        : null,
                                    borderRadius: BorderRadius.circular(6),
                                    child: Icon(
                                      Icons.remove,
                                      color: (card['quantity'] as int) > 1
                                          ? Colors.grey.shade600
                                          : Colors.grey.shade400,
                                      size: 16,
                                    ),
                                  ),
                                ),
                              ),

                              const SizedBox(width: 8),

                              // حقل الكمية بنفس تصميم السعر
                              Expanded(
                                child: GestureDetector(
                                  onTap: () => _editCardQuantity(index, card),
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 12,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                          color: Colors.grey.shade300),
                                    ),
                                    child: Center(
                                      child: Text(
                                        '${card['quantity']}',
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black87,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                                ),
                              ),

                              const SizedBox(width: 8),

                              // زر الزيادة
                              Container(
                                width: 28,
                                height: 28,
                                decoration: BoxDecoration(
                                  color: Colors.green.shade100,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: () {
                                      setState(() {
                                        card['quantity'] =
                                            (card['quantity'] as int) + 1;
                                      });
                                    },
                                    borderRadius: BorderRadius.circular(6),
                                    child: const Icon(
                                      Icons.add,
                                      color: Colors.green,
                                      size: 16,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // قسم السعر (في المكان الثاني)
                Expanded(
                  child: Column(
                    children: [
                      // أيقونة القفل والسعر
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Icon(
                              Icons.lock_outline,
                              color: Colors.grey.shade600,
                              size: 16,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'السعر',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 4),
                          const Icon(
                            Icons.attach_money,
                            color: Colors.green,
                            size: 16,
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // قيمة السعر مع إمكانية التعديل وأزرار سريعة
                      Column(
                        children: [
                          GestureDetector(
                            onTap: () => _editCardPrice(index, card),
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 12,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey.shade300),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.edit,
                                    color: Colors.green.shade600,
                                    size: 14,
                                  ),
                                  const SizedBox(width: 6),
                                  Flexible(
                                    child: Text(
                                      'د ع ${_formatNumber(card['price'].toInt())}',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 0.5),

          // المجموع الأخضر في الأسفل
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.shade400,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Row(
                  children: [
                    Icon(Icons.calculate, color: Colors.white, size: 20),
                    SizedBox(width: 8),
                    Text(
                      'المجموع:',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                Text(
                  'د ع ${_formatNumber((card['quantity'] * card['price']).toInt())}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // تعديل كمية الكارت
  void _editCardQuantity(int index, Map<String, dynamic> card) {
    final quantityController = TextEditingController(
      text: '${card['quantity']}',
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.edit, color: Colors.blue.shade600, size: 24),
            ),
            const SizedBox(width: 12),
            Text(
              'تعديل الكمية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
          ],
        ),
        content: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'أدخل الكمية الجديدة لـ ${card['name']}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: quantityController,
                keyboardType: TextInputType.number,
                textAlign: TextAlign.center,
                autofocus: true,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
                decoration: InputDecoration(
                  labelText: 'الكمية الجديدة',
                  labelStyle: TextStyle(color: Colors.grey.shade600),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: Colors.blue.shade600, width: 2),
                  ),
                  prefixIcon: Icon(Icons.numbers, color: Colors.blue.shade600),
                  filled: true,
                  fillColor: Colors.grey.shade50,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              final newQuantity = int.tryParse(quantityController.text);
              if (newQuantity != null && newQuantity > 0) {
                setState(() {
                  _selectedCardTypes[index]['quantity'] = newQuantity;
                });
                Navigator.pop(context);

                // رسالة تأكيد
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Row(
                      children: [
                        const Icon(Icons.check_circle, color: Colors.white),
                        const SizedBox(width: 8),
                        Text(
                          'تم تحديث الكمية إلى $newQuantity',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    backgroundColor: Colors.green.shade600,
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    duration: const Duration(seconds: 2),
                  ),
                );
              } else {
                // رسالة خطأ
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Row(
                      children: [
                        Icon(Icons.error, color: Colors.white),
                        SizedBox(width: 8),
                        Text(
                          'يرجى إدخال كمية صحيحة أكبر من 0',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    backgroundColor: Colors.red.shade600,
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    duration: const Duration(seconds: 2),
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'حفظ',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // حذف كارت احترافي مع تأكيد
  void _showDeleteConfirmation(int index) {
    final card = _selectedCardTypes[index];
    showDialog(
      context: context,
      barrierColor: Colors.black54,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          margin: const EdgeInsets.all(20),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // أيقونة التحذير
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(40),
                  border: Border.all(color: Colors.red.shade200, width: 2),
                ),
                child: Icon(
                  Icons.delete_forever_rounded,
                  color: Colors.red.shade600,
                  size: 40,
                ),
              ),

              const SizedBox(height: 20),

              // العنوان
              Text(
                'تأكيد الحذف',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 12),

              // الرسالة
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  children: [
                    Text(
                      'هل تريد حذف',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: _getCardTypeColor(
                          card['name'],
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: _getCardTypeColor(
                            card['name'],
                          ).withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        '"${card['name']}"',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: _getCardTypeColor(card['name']),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'من القائمة؟',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // الأزرار
              Row(
                children: [
                  // زر الإلغاء
                  Expanded(
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () => Navigator.pop(context),
                          borderRadius: BorderRadius.circular(12),
                          child: Center(
                            child: Text(
                              'إلغاء',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey.shade700,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // زر الحذف
                  Expanded(
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.red.shade500, Colors.red.shade600],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.red.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            setState(() {
                              _selectedCardTypes.removeAt(index);
                            });
                            Navigator.pop(context);

                            // رسالة نجاح
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Row(
                                  children: [
                                    const Icon(
                                      Icons.check_circle,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'تم حذف ${card['name']} بنجاح',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                backgroundColor: Colors.green.shade600,
                                behavior: SnackBarBehavior.floating,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                margin: const EdgeInsets.all(16),
                                duration: const Duration(seconds: 3),
                              ),
                            );
                          },
                          borderRadius: BorderRadius.circular(12),
                          child: const Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.delete_outline,
                                  color: Colors.white,
                                  size: 20,
                                ),
                                SizedBox(width: 8),
                                Text(
                                  'حذف',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // عرض علامة صح متحركة مع تأثير جميل
  void _showAnimatedCheckmark(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
      builder: (BuildContext context) {
        return const AnimatedCheckmarkWidget();
      },
    );
  }

  Future<void> _selectDate(BuildContext context, bool isEntryDate) async {
    // إغلاق قائمة أنواع الكروت المنسدلة عند النقر على التاريخ
    _closeCardTypeDropdown();

    // حفظ مرجع للـ context قبل العمليات غير المتزامنة
    final BuildContext savedContext = context;

    final DateTime? picked = await _showCustomDatePicker(
      context: savedContext,
      initialDate: isEntryDate ? _entryDate : _dueDate,
      firstDate: isEntryDate
          ? DateTime.now().subtract(const Duration(days: 365))
          : DateTime(2020), // السماح باختيار تواريخ سابقة لتاريخ الاستحقاق
      lastDate: DateTime.now().add(const Duration(days: 365)),
      helpText: isEntryDate ? 'اختيار تاريخ القيد' : 'اختيار تاريخ الاستحقاق',
      isEntryDate: isEntryDate,
    );

    if (picked != null) {
      if (isEntryDate) {
        // لتاريخ القيد، اسأل المستخدم عن الوقت أيضاً
        if (!mounted) return;
        final TimeOfDay? pickedTime = await showTimePicker(
          context: context,
          initialTime: TimeOfDay.fromDateTime(_entryDate),
          helpText: 'اختيار وقت القيد',
          cancelText: 'إلغاء',
          confirmText: 'تأكيد',
          builder: (context, child) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: ColorScheme.light(
                  primary: Colors.blue.shade600,
                  onSurface: Colors.black87,
                ),
                timePickerTheme: TimePickerThemeData(
                  backgroundColor: Colors.white,
                  hourMinuteShape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  dayPeriodShape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  dialHandColor: Colors.blue.shade600,
                  dialBackgroundColor: Colors.blue.shade50,
                  hourMinuteColor: Colors.blue.shade50,
                  hourMinuteTextColor: Colors.blue.shade800,
                  dayPeriodColor: Colors.blue.shade100,
                  dayPeriodTextColor: Colors.blue.shade800,
                  entryModeIconColor: Colors.blue.shade600,
                ),
              ),
              child: child!,
            );
          },
        );

        if (mounted) {
          setState(() {
            if (pickedTime != null) {
              _entryDate = DateTime(
                picked.year,
                picked.month,
                picked.day,
                pickedTime.hour,
                pickedTime.minute,
              );
            } else {
              // إذا لم يختر وقت، احتفظ بالوقت الحالي
              final currentTime = TimeOfDay.fromDateTime(_entryDate);
              _entryDate = DateTime(
                picked.year,
                picked.month,
                picked.day,
                currentTime.hour,
                currentTime.minute,
              );
            }
          });
        }
      } else {
        // لتاريخ الاستحقاق، نضع الوقت في نهاية اليوم
        setState(() {
          _dueDate = DateTime(picked.year, picked.month, picked.day, 23, 59);
        });
      }
    }
  }

  // منتقي تاريخ مخصص مع أسماء الأشهر بالأرقام العربية
  Future<DateTime?> _showCustomDatePicker({
    required BuildContext context,
    required DateTime initialDate,
    required DateTime firstDate,
    required DateTime lastDate,
    required String helpText,
    required bool isEntryDate,
  }) async {
    return ArabicMonthDatePicker.showArabicDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      helpText: helpText,
      isEntryDate: isEntryDate,
    );
  }

  void _showCustomerSelectionDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _CustomerSelectionBottomSheet(
        onCustomerSelected: (customer) {
          setState(() {
            _selectedCustomer = customer;
            _customerNameController.text = customer.name;
          });
        },
      ),
    );
  }

  void _showCardTypeManagementDialog() {
    // إعداد القائمة المؤقتة للاختيار المتعدد
    _tempSelectedCardTypes.clear();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          height: MediaQuery.of(context).size.height * 0.9,
          margin: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(25),
              topRight: Radius.circular(25),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black26,
                blurRadius: 10,
                offset: Offset(0, -2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue.shade800, Colors.blue.shade900],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25),
                    topRight: Radius.circular(25),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Handle bar
                    Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.credit_card,
                            color: Colors.white,
                            size: 28,
                          ),
                        ),
                        const SizedBox(width: 16),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'اختيار نوع الكارت',
                                style: TextStyle(
                                  fontSize: 22,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                'اختر من الأنواع المتاحة أو أضف نوع جديد',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.white70,
                                ),
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 28,
                          ),
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.white.withValues(
                              alpha: 0.2,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: Consumer<CardTypeProvider>(
                  builder: (context, provider, _) {
                    return SingleChildScrollView(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Custom card types section
                          _buildCardTypesSection(
                            'الأنواع المخصصة',
                            Icons.edit,
                            Colors.purple,
                            _buildCustomCardTypes(provider, setModalState),
                          ),

                          const SizedBox(height: 24),

                          // Add new card type section
                          if (!_isMultipleCardMode)
                            _buildAddNewCardTypeSection(provider),

                          const SizedBox(height: 20),

                          // زر تأكيد للوضع المتعدد
                          if (_isMultipleCardMode) ...[
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.blue.shade50,
                                    Colors.blue.shade100,
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(color: Colors.blue.shade200),
                              ),
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.info_outline,
                                        color: Colors.blue.shade600,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'تم اختيار ${_tempSelectedCardTypes.length} كارت',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.blue.shade700,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  SizedBox(
                                    width: double.infinity,
                                    child: ElevatedButton.icon(
                                      onPressed:
                                          _tempSelectedCardTypes.isNotEmpty
                                              ? () => _confirmMultipleSelection(
                                                    setModalState,
                                                  )
                                              : null,
                                      icon: const Icon(
                                        Icons.check_circle,
                                        size: 20,
                                      ),
                                      label: const Text(
                                        'تأكيد الاختيار',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor:
                                            _tempSelectedCardTypes.isNotEmpty
                                                ? Colors.green.shade600
                                                : Colors.grey.shade300,
                                        foregroundColor: Colors.white,
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 16,
                                        ),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        elevation:
                                            _tempSelectedCardTypes.isNotEmpty
                                                ? 3
                                                : 0,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 20),
                          ],
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء قسم أنواع الكروت
  Widget _buildCardTypesSection(
    String title,
    IconData icon,
    Color color,
    Widget content,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        content,
      ],
    );
  }

  // بناء الأنواع المخصصة
  Widget _buildCustomCardTypes(
    CardTypeProvider provider,
    StateSetter setModalState,
  ) {
    if (provider.customCardTypes.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Column(
          children: [
            Icon(Icons.inbox, color: Colors.grey.shade400, size: 48),
            const SizedBox(height: 12),
            Text(
              'لا توجد أنواع مخصصة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'يمكنك إضافة أنواع كروت جديدة من الأسفل',
              style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
            ),
          ],
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // حساب عدد الأعمدة بناءً على عرض الشاشة
        final screenWidth = constraints.maxWidth;
        const cardWidth = 160.0; // عرض البطاقة المطلوب
        const spacing = 12.0;
        final crossAxisCount = ((screenWidth + spacing) / (cardWidth + spacing))
            .floor()
            .clamp(1, 3);

        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
            childAspectRatio: 0.85, // نسبة أطول للمحتوى
          ),
          itemCount: provider.customCardTypes.length,
          itemBuilder: (context, index) {
            final cardType = provider.customCardTypes[index];
            final cardTypeId = 'custom_${cardType.id}';
            final isSelected = _isMultipleCardMode
                ? _tempSelectedCardTypes.contains(cardTypeId)
                : _selectedCardType == cardTypeId;

            return GestureDetector(
              onTap: () {
                if (_isMultipleCardMode) {
                  // وضع الاختيار المتعدد
                  setModalState(() {
                    if (_tempSelectedCardTypes.contains(cardTypeId)) {
                      _tempSelectedCardTypes.remove(cardTypeId);
                    } else {
                      _tempSelectedCardTypes.add(cardTypeId);
                    }
                  });
                } else {
                  // وضع الاختيار الواحد
                  setState(() {
                    _selectedCardType = cardTypeId;
                    _cardTypeController.text = cardType.displayName;

                    // تطبيق السعر الثابت إذا كان متوفراً
                    final formDataProvider = Provider.of<FormDataProvider>(
                      context,
                      listen: false,
                    );
                    final savedPrice = formDataProvider.getCardTypePrice(
                      cardType.displayName,
                    );
                    if (savedPrice != null) {
                      _amountController.text = savedPrice.toString();
                    }

                    // إضافة الكارت فوراً إلى القائمة المحددة
                    _addSelectedCardToList(
                      cardTypeId,
                      cardType.displayName,
                      savedPrice ?? 1000.0,
                    );
                  });
                  Navigator.of(context).pop();
                }
              },
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: isSelected
                        ? [
                            Colors.purple.withValues(alpha: 0.1),
                            Colors.purple.withValues(alpha: 0.2),
                          ]
                        : [Colors.white, Colors.grey.shade50],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: isSelected ? Colors.purple : Colors.grey.shade300,
                    width: isSelected ? 2 : 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: isSelected
                          ? Colors.purple.withValues(alpha: 0.2)
                          : Colors.grey.withValues(alpha: 0.1),
                      blurRadius: isSelected ? 8 : 4,
                      offset: const Offset(0, 2),
                      spreadRadius: isSelected ? 1 : 0,
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    // المحتوى الرئيسي
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // أيقونة الكارت
                          Container(
                            width: 45,
                            height: 45,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.teal.withValues(alpha: 0.8),
                                  Colors.teal,
                                  Colors.teal.withValues(alpha: 1.2),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.teal.withValues(alpha: 0.3),
                                  blurRadius: 6,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                            ),
                            child: Stack(
                              children: [
                                // خلفية الكارت
                                Positioned.fill(
                                  child: Container(
                                    margin: const EdgeInsets.all(6),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topCenter,
                                        end: Alignment.bottomCenter,
                                        colors: [
                                          Colors.white.withValues(alpha: 0.3),
                                          Colors.white.withValues(alpha: 0.1),
                                        ],
                                      ),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                ),
                                // الأيقونة
                                Center(
                                  child: Icon(
                                    Icons.credit_card_rounded,
                                    color: Colors.white,
                                    size: 20,
                                    shadows: [
                                      Shadow(
                                        color: Colors.black.withValues(
                                          alpha: 0.3,
                                        ),
                                        offset: const Offset(1, 1),
                                        blurRadius: 2,
                                      ),
                                    ],
                                  ),
                                ),
                                // لمعة علوية
                                Positioned(
                                  top: 4,
                                  left: 4,
                                  right: 4,
                                  child: Container(
                                    height: 12,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topCenter,
                                        end: Alignment.bottomCenter,
                                        colors: [
                                          Colors.white.withValues(alpha: 0.6),
                                          Colors.white.withValues(alpha: 0.0),
                                        ],
                                      ),
                                      borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(10),
                                        topRight: Radius.circular(10),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 10),

                          // اسم الكارت
                          Flexible(
                            child: Text(
                              cardType.displayName,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.w600,
                                color: isSelected
                                    ? Colors.purple.shade700
                                    : Colors.black87,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),

                          const SizedBox(height: 10),

                          // أزرار الإجراءات
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              // زر الإضافة/الإزالة
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    if (_isMultipleCardMode) {
                                      // وضع الاختيار المتعدد
                                      setModalState(() {
                                        if (_tempSelectedCardTypes.contains(
                                          cardTypeId,
                                        )) {
                                          _tempSelectedCardTypes.remove(
                                            cardTypeId,
                                          );
                                        } else {
                                          _tempSelectedCardTypes.add(
                                            cardTypeId,
                                          );
                                        }
                                      });
                                    } else {
                                      // وضع الاختيار الواحد
                                      setState(() {
                                        _selectedCardType = cardTypeId;
                                        _cardTypeController.text =
                                            cardType.displayName;

                                        // تطبيق السعر الثابت إذا كان متوفراً
                                        final formDataProvider =
                                            Provider.of<FormDataProvider>(
                                          context,
                                          listen: false,
                                        );
                                        final savedPrice =
                                            formDataProvider.getCardTypePrice(
                                          cardType.displayName,
                                        );
                                        if (savedPrice != null) {
                                          _amountController.text =
                                              savedPrice.toString();
                                        }

                                        // إضافة الكارت فوراً إلى القائمة المحددة
                                        _addSelectedCardToList(
                                          cardTypeId,
                                          cardType.displayName,
                                          savedPrice ?? 1000.0,
                                        );
                                      });
                                      Navigator.of(context).pop();
                                    }
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 10,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: isSelected
                                            ? [
                                                Colors.red.shade400,
                                                Colors.red.shade600,
                                              ]
                                            : [
                                                Colors.green.shade400,
                                                Colors.green.shade600,
                                              ],
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                      boxShadow: [
                                        BoxShadow(
                                          color: (isSelected
                                                  ? Colors.red
                                                  : Colors.green)
                                              .withValues(alpha: 0.3),
                                          blurRadius: 3,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          isSelected ? Icons.remove : Icons.add,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          isSelected ? 'إزالة' : 'إضافة',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 12,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),

                              const SizedBox(width: 6),

                              // زر الحذف
                              GestureDetector(
                                onTap: () => _deleteCardTypeInDialog(
                                  cardType.id!,
                                  provider,
                                ),
                                child: Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.red.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: Colors.red.withValues(alpha: 0.3),
                                    ),
                                  ),
                                  child: const Icon(
                                    Icons.delete_outline,
                                    color: Colors.red,
                                    size: 16,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  // بناء قسم إضافة نوع جديد
  Widget _buildAddNewCardTypeSection(CardTypeProvider provider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade50, Colors.green.shade100],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.add_card,
                  color: Colors.green.shade700,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إضافة نوع جديد',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'أضف نوع كارت مخصص جديد',
                      style: TextStyle(fontSize: 14, color: Colors.green),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _newCardTypeController,
            decoration: InputDecoration(
              hintText: 'اسم نوع الكارت الجديد',
              hintStyle: TextStyle(color: Colors.grey.shade600, fontSize: 16),
              prefixIcon: Icon(Icons.credit_card, color: Colors.green.shade600),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.green.shade600, width: 2),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
              fontWeight: FontWeight.w600,
            ),
            cursorColor: Colors.green.shade600,
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _addNewCardType(provider),
              icon: const Icon(Icons.add, size: 20),
              label: const Text(
                'إضافة نوع الكارت',
                style: TextStyle(fontSize: 16),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _addNewCardType(CardTypeProvider provider) async {
    final name = _newCardTypeController.text.trim();
    if (name.isNotEmpty) {
      try {
        await provider.addCustomCardType(name);
        _newCardTypeController.clear();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة نوع الكارت بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في إضافة نوع الكارت: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // تأكيد الاختيار المتعدد
  void _confirmMultipleSelection(StateSetter setModalState) {
    if (_tempSelectedCardTypes.isEmpty) return;

    // تحديث النص في الحقل ليعكس الاختيارات المتعددة
    setState(() {
      _cardTypeController.text = '${_tempSelectedCardTypes.length} كارت محدد';
    });

    Navigator.of(context).pop();
  }

  void _deleteCardTypeInDialog(
    int cardTypeId,
    CardTypeProvider provider,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.delete_outline,
                color: Colors.red.shade600,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'تأكيد الحذف',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
        content: const Text(
          'هل أنت متأكد من حذف نوع الكارت هذا؟',
          style: TextStyle(fontSize: 16, color: Colors.black87),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey.shade700,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            ),
            child: const Text(
              'إلغاء',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'حذف',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await provider.deleteCustomCardType(cardTypeId);

        if (_selectedCardType == 'custom_$cardTypeId') {
          setState(() {
            _selectedCardType = null;
            _cardTypeController.clear();
          });
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف نوع الكارت بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف نوع الكارت: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _showNotesDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(28),
            topRight: Radius.circular(28),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Column(
          children: [
            // Enhanced Header
            Container(
              padding: const EdgeInsets.fromLTRB(24, 16, 24, 20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.indigo.shade600,
                    Colors.purple.shade600,
                    Colors.deepPurple.shade700,
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(28),
                  topRight: Radius.circular(28),
                ),
              ),
              child: Column(
                children: [
                  // Handle bar
                  Container(
                    width: 50,
                    height: 5,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.4),
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      // Icon container with animation effect
                      Container(
                        padding: const EdgeInsets.all(14),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.2),
                          ),
                        ),
                        child: const Icon(
                          Icons.edit_note_rounded,
                          color: Colors.white,
                          size: 32,
                        ),
                      ),
                      const SizedBox(width: 16),
                      const Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'إدارة الملاحظات',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                letterSpacing: 0.5,
                              ),
                            ),
                            SizedBox(height: 6),
                            Text(
                              'أضف ملاحظات مهمة وتفاصيل إضافية للدين',
                              style: TextStyle(
                                fontSize: 15,
                                color: Colors.white70,
                                height: 1.3,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Close button with better design
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(
                            Icons.close_rounded,
                            color: Colors.white,
                            size: 24,
                          ),
                          padding: const EdgeInsets.all(8),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Content with better spacing
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(20, 24, 20, 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Enhanced Quick notes section - أولاً
                    _buildEnhancedQuickNotesSection(),

                    const SizedBox(height: 28),

                    // Enhanced Input section - ثانياً
                    _buildEnhancedNotesInputSection(),

                    const SizedBox(height: 20),

                    // نص توضيحي
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.lightbulb_outline,
                            color: Colors.blue.shade600,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'اضغط على الملاحظات السريعة للإضافة الفورية، أو اكتب يدوياً واضغط تأكيد',
                              style: TextStyle(
                                color: Colors.blue.shade700,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // زر التأكيد للكتابة اليدوية
                    Container(
                      width: double.infinity,
                      height: 56,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.indigo.withValues(alpha: 0.3),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ElevatedButton.icon(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.check_circle_outline, size: 22),
                        label: const Text(
                          'تأكيد وحفظ الملاحظات',
                          style: TextStyle(
                            fontSize: 17,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 0.5,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.indigo.shade600,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء قسم إدخال الملاحظات المحسن
  Widget _buildEnhancedNotesInputSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.indigo.shade50,
            Colors.purple.shade50,
            Colors.blue.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.indigo.withValues(alpha: 0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.indigo.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon and title
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.indigo.shade400, Colors.purple.shade400],
                  ),
                  borderRadius: BorderRadius.circular(14),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.indigo.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.edit_note_rounded,
                  color: Colors.white,
                  size: 26,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'كتابة الملاحظات',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.indigo,
                        letterSpacing: 0.5,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'أضف تفاصيل مهمة حول الدين أو العميل',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.indigo,
                        height: 1.3,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Enhanced text field
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.indigo.withValues(alpha: 0.2)),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              controller: _notesController,
              maxLines: 3,
              style: const TextStyle(
                color: Colors.black87,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                height: 1.4,
              ),
              decoration: InputDecoration(
                hintText: 'اكتب ملاحظاتك هنا...',
                hintStyle: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 15,
                  height: 1.3,
                ),
                prefixIcon: Padding(
                  padding: const EdgeInsets.only(top: 8, right: 12),
                  child: Icon(
                    Icons.sticky_note_2_outlined,
                    color: Colors.indigo.shade400,
                    size: 20,
                  ),
                ),
                suffixIcon: _notesController.text.isNotEmpty
                    ? Container(
                        margin: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: IconButton(
                          onPressed: () {
                            setState(() {
                              _notesController.clear();
                            });
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: const Row(
                                  children: [
                                    Icon(
                                      Icons.check_circle,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      'تم مسح الملاحظات',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                backgroundColor: Colors.green.shade600,
                                behavior: SnackBarBehavior.floating,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                margin: const EdgeInsets.all(16),
                                duration: const Duration(seconds: 2),
                              ),
                            );
                          },
                          icon: Icon(
                            Icons.delete_outline,
                            color: Colors.red.shade600,
                          ),
                          tooltip: 'مسح الملاحظات',
                        ),
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
              ),
              onChanged: (value) {
                setState(() {}); // لتحديث زر الحذف
              },
            ),
          ),

          // Character counter
          if (_notesController.text.isNotEmpty) ...[
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${_notesController.text.length} حرف',
                  style: TextStyle(
                    color: Colors.indigo.shade600,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (_notesController.text.length > 100)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'ملاحظة مفصلة ✓',
                      style: TextStyle(
                        color: Colors.green.shade700,
                        fontSize: 11,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  // بناء قسم الملاحظات السريعة المحسن
  Widget _buildEnhancedQuickNotesSection() {
    return Consumer<FormDataProvider>(
      builder: (context, formDataProvider, _) {
        final quickNotes = formDataProvider.quickNotes;

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.grey.shade200),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header مبسط
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.indigo.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.sticky_note_2_outlined,
                      color: Colors.indigo.shade600,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'الملاحظات السريعة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.indigo,
                    ),
                  ),
                  const Spacer(),
                  // زر حذف جميع الملاحظات
                  IconButton(
                    onPressed: () => _showDeleteAllNotesConfirmation(),
                    icon: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.delete_sweep_outlined,
                        color: Colors.red.shade600,
                        size: 18,
                      ),
                    ),
                    tooltip: 'حذف جميع الملاحظات',
                  ),
                  // زر إدارة الملاحظات
                  IconButton(
                    onPressed: () => _showQuickNotesManagementDialog(),
                    icon: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.grey.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.settings_outlined,
                        color: Colors.grey.shade600,
                        size: 18,
                      ),
                    ),
                    tooltip: 'إدارة الملاحظات',
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Quick clear button
              if (_notesController.text.isNotEmpty) ...[
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.only(bottom: 16),
                  child: ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _notesController.clear();
                      });
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Row(
                            children: [
                              Icon(
                                Icons.check_circle,
                                color: Colors.white,
                                size: 20,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'تم مسح جميع الملاحظات',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          backgroundColor: Colors.green.shade600,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          margin: const EdgeInsets.all(16),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
                    icon: Icon(
                      Icons.delete_sweep_rounded,
                      color: Colors.red.shade600,
                    ),
                    label: Text(
                      'مسح جميع الملاحظات',
                      style: TextStyle(
                        color: Colors.red.shade600,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade50,
                      elevation: 0,
                      side: BorderSide(color: Colors.red.shade200),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],

              // Quick notes list - محسن للأداء
              if (quickNotes.isNotEmpty) ...[
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: quickNotes.length,
                  // تحسين أداء القائمة
                  addAutomaticKeepAlives: false,
                  addSemanticIndexes: false,
                  itemBuilder: (context, index) {
                    final note = quickNotes[index];
                    return RepaintBoundary(
                      child: _buildQuickNoteItem(note, formDataProvider),
                    );
                  },
                ),
              ] else ...[
                // Empty state
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.note_add_outlined,
                        color: Colors.grey.shade400,
                        size: 48,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'لا توجد ملاحظات سريعة',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'اكتب ملاحظة في الحقل أعلاه وسيتم حفظها تلقائياً',
                        style: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  // بناء عنصر ملاحظة سريعة - مثل تصميم الكارتات
  Widget _buildQuickNoteItem(String note, FormDataProvider formDataProvider) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            // إضافة فورية بدون تأكيد
            setState(() {
              if (_notesController.text.isNotEmpty) {
                _notesController.text += ', $note';
              } else {
                _notesController.text = note;
              }
            });

            // إغلاق النافذة تلقائياً بعد الاختيار
            Navigator.of(context).pop();

            // رسالة تأكيد سريعة
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: 18,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'تم إضافة: $note',
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                  ],
                ),
                backgroundColor: Colors.green.shade600,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                margin: const EdgeInsets.all(16),
                duration: const Duration(milliseconds: 800),
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                // أيقونة الملاحظة
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.indigo.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.sticky_note_2_outlined,
                    color: Colors.indigo.shade600,
                    size: 20,
                  ),
                ),

                const SizedBox(width: 12),

                // نص الملاحظة
                Expanded(
                  child: Text(
                    note,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                ),

                // زر الحذف
                GestureDetector(
                  onTap: () {
                    // منع انتشار الحدث للـ InkWell الرئيسي
                    _showDeleteNoteConfirmation(note, formDataProvider);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      Icons.delete_outline,
                      color: Colors.red,
                      size: 20,
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // أيقونة الإضافة
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: const Icon(
                    Icons.add_circle_outline,
                    color: Colors.green,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // نافذة إدارة الملاحظات السريعة مع إضافة ملاحظات جديدة
  void _showQuickNotesManagementDialog() {
    final TextEditingController newNoteController = TextEditingController();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(25),
              topRight: Radius.circular(25),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12),
                width: 50,
                height: 5,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(3),
                ),
              ),

              // Header
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.indigo.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.sticky_note_2_outlined,
                        color: Colors.indigo.shade600,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'إدارة الملاحظات السريعة',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.indigo,
                      ),
                    ),
                  ],
                ),
              ),

              // قسم إضافة ملاحظة جديدة
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.green.shade50, Colors.green.shade100],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.green.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.add_circle_outline,
                            color: Colors.green.shade700,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Text(
                          'إضافة ملاحظة جديدة',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    TextField(
                      controller: newNoteController,
                      decoration: InputDecoration(
                        hintText: 'اكتب الملاحظة الجديدة...',
                        hintStyle: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: 16,
                        ),
                        prefixIcon: Icon(
                          Icons.edit_note,
                          color: Colors.green.shade600,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.green.shade300),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.green.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: Colors.green.shade600,
                            width: 2,
                          ),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black87,
                        fontWeight: FontWeight.w500,
                      ),
                      onSubmitted: (value) => _addNewQuickNote(
                        value,
                        newNoteController,
                        setModalState,
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => _addNewQuickNote(
                          newNoteController.text,
                          newNoteController,
                          setModalState,
                        ),
                        icon: const Icon(Icons.add, size: 20),
                        label: const Text(
                          'إضافة الملاحظة',
                          style: TextStyle(fontSize: 16),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Content - قائمة الملاحظات
              Expanded(
                child: Consumer<FormDataProvider>(
                  builder: (context, formDataProvider, _) {
                    final quickNotes = formDataProvider.quickNotes;

                    if (quickNotes.isEmpty) {
                      return SingleChildScrollView(
                        padding: const EdgeInsets.all(20),
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(32),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade50,
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(50),
                                ),
                                child: Icon(
                                  Icons.note_add_outlined,
                                  color: Colors.grey.shade400,
                                  size: 48,
                                ),
                              ),
                              const SizedBox(height: 20),
                              Text(
                                'لا توجد ملاحظات سريعة',
                                style: TextStyle(
                                  color: Colors.grey.shade700,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'أضف ملاحظة جديدة من الأعلى لتبدأ في استخدام الملاحظات السريعة',
                                style: TextStyle(
                                  color: Colors.grey.shade500,
                                  fontSize: 14,
                                  height: 1.4,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      );
                    }

                    return ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      itemCount: quickNotes.length,
                      // تحسين أداء القائمة
                      addAutomaticKeepAlives: false,
                      addSemanticIndexes: false,
                      itemBuilder: (context, index) {
                        final note = quickNotes[index];
                        return RepaintBoundary(
                          child: _buildManagementQuickNoteItem(
                            note,
                            formDataProvider,
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // إضافة ملاحظة سريعة جديدة
  void _addNewQuickNote(
    String noteText,
    TextEditingController controller,
    StateSetter setModalState,
  ) async {
    final trimmedNote = noteText.trim();
    if (trimmedNote.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(Icons.warning, color: Colors.white, size: 20),
              SizedBox(width: 8),
              Text(
                'يرجى كتابة نص الملاحظة',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          backgroundColor: Colors.orange.shade600,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(16),
          duration: const Duration(seconds: 2),
        ),
      );
      return;
    }

    try {
      final formDataProvider = Provider.of<FormDataProvider>(
        context,
        listen: false,
      );
      await formDataProvider.addQuickNote(trimmedNote);

      // مسح الحقل
      controller.clear();

      // تحديث الواجهة
      setModalState(() {});

      // رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  'تم إضافة "$trimmedNote" بنجاح',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            backgroundColor: Colors.green.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  'خطأ في إضافة الملاحظة: $e',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            backgroundColor: Colors.red.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // بناء عنصر ملاحظة في نافذة الإدارة - بدون زر إضافة
  Widget _buildManagementQuickNoteItem(
    String note,
    FormDataProvider formDataProvider,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.indigo.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.sticky_note_2_outlined,
            color: Colors.indigo.shade600,
            size: 20,
          ),
        ),
        title: Text(
          note,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        trailing: IconButton(
          onPressed: () => _showDeleteNoteConfirmation(note, formDataProvider),
          icon: Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.delete_outline,
              color: Colors.red,
              size: 18,
            ),
          ),
          tooltip: 'حذف',
        ),
      ),
    );
  }

  // نافذة تأكيد حذف جميع الملاحظات السريعة
  void _showDeleteAllNotesConfirmation() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.delete_sweep_outlined,
                color: Colors.red.shade600,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'حذف جميع الملاحظات',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'هل أنت متأكد من حذف جميع الملاحظات السريعة؟',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning_amber_outlined,
                    color: Colors.orange.shade600,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'سيتم حذف جميع الملاحظات بما في ذلك الافتراضية',
                      style: TextStyle(
                        color: Colors.orange.shade700,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'حذف الكل',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );

    if (result == true && mounted) {
      try {
        final formDataProvider = Provider.of<FormDataProvider>(
          context,
          listen: false,
        );
        await formDataProvider.clearAllQuickNotes();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'تم حذف جميع الملاحظات السريعة بنجاح',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              backgroundColor: Colors.green.shade600,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              margin: const EdgeInsets.all(16),
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'خطأ في حذف الملاحظات: $e',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              backgroundColor: Colors.red.shade600,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              margin: const EdgeInsets.all(16),
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    }
  }

  // إظهار تأكيد حذف الملاحظة
  void _showDeleteNoteConfirmation(
    String note,
    FormDataProvider formDataProvider,
  ) async {
    final bool? shouldDelete = await showDialog<bool>(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          margin: const EdgeInsets.all(20),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // أيقونة التحذير
              Container(
                width: 70,
                height: 70,
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(35),
                  border: Border.all(color: Colors.red.shade200, width: 2),
                ),
                child: Icon(
                  Icons.delete_forever_rounded,
                  color: Colors.red.shade600,
                  size: 36,
                ),
              ),

              const SizedBox(height: 20),

              Text(
                'حذف الملاحظة',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),

              const SizedBox(height: 12),

              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    const Text(
                      'هل تريد حذف الملاحظة',
                      style: TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade100,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.orange.shade300),
                      ),
                      child: Text(
                        '"$note"',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'نهائياً؟',
                      style: TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context, false),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade100,
                        foregroundColor: Colors.grey.shade700,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text(
                        'إلغاء',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context, true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red.shade600,
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.delete_outline, size: 20),
                          SizedBox(width: 6),
                          Text(
                            'حذف',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );

    if (shouldDelete == true && mounted) {
      await formDataProvider.removeQuickNote(note);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  'تم حذف "$note" بنجاح',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            backgroundColor: Colors.green.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  // Clear form method
  void clearForm() {
    setState(() {
      _selectedCustomer = null;
      _selectedCardType = null;
      _customerNameController.clear();
      _quantityController.clear();
      _amountController.clear();
      _notesController.clear();
      _entryDate = DateTime.now();
      _dueDate = DateTime.now().add(const Duration(days: 7));
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم مسح النموذج'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  // Reset quick values method
  void resetQuickValues() async {
    final formDataProvider = Provider.of<FormDataProvider>(
      context,
      listen: false,
    );
    await formDataProvider.resetQuickValuesToDefaults();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إعادة تعيين القيم السريعة'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  // Build section header
  Widget buildSectionHeader(String title, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.grey.shade700,
        ),
      ),
    );
  }

  // Build enhanced clickable field
  Widget buildEnhancedClickableField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required VoidCallback onTap,
    String? Function(String?)? validator,
    required Color color,
    required bool hasValue,
  }) {
    // تحديد لون الأيقونة حسب نوع الحقل
    Color iconColor;
    if (icon == Icons.person_outline) {
      iconColor = Colors.blue;
    } else if (icon == Icons.credit_card_outlined) {
      iconColor = Colors.purple;
    } else if (icon == Icons.numbers_outlined) {
      iconColor = Colors.green;
    } else if (icon == Icons.attach_money_outlined) {
      iconColor = Colors.orange;
    } else if (icon == Icons.sticky_note_2_outlined) {
      iconColor = Colors.indigo;
    } else {
      iconColor = Colors.grey.shade600;
    }

    return TextFormField(
      controller: controller,
      readOnly: true,
      onTap: onTap,
      validator: validator,
      style: TextStyle(
        color: hasValue ? Colors.black87 : Colors.grey.shade600,
        fontSize: 16,
        fontWeight: hasValue ? FontWeight.w500 : FontWeight.normal,
      ),
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: iconColor, size: 22),
        suffixIcon: hasValue && icon == Icons.credit_card_outlined
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: IconButton(
                      onPressed: () {
                        setState(() {
                          controller.clear();
                          _selectedCardType = null;
                        });

                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تم مسح نوع الكارت'),
                            backgroundColor: Colors.green,
                            duration: Duration(seconds: 1),
                          ),
                        );
                      },
                      icon: Icon(
                        Icons.delete_outline,
                        color: Colors.red.shade600,
                        size: 18,
                      ),
                      tooltip: 'مسح',
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey.shade400,
                    size: 16,
                  ),
                ],
              )
            : Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey.shade400,
                size: 16,
              ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade500, width: 1.5),
        ),
        filled: true,
        fillColor: Colors.white,
        labelStyle: TextStyle(
          color: Colors.grey.shade600,
          fontWeight: FontWeight.normal,
        ),
      ),
    );
  }

  // Build smart button - زر ذكي متطور مع تحسينات الأداء
  Widget _buildSmartButton({
    required VoidCallback onTap,
    required IconData icon,
    required Color color,
    required bool isActive,
    required String tooltip,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.white, Colors.grey.shade50],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isActive ? color.withValues(alpha: 0.3) : Colors.grey.shade300,
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.8),
            blurRadius: 2,
            offset: const Offset(-1, -1),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            HapticFeedback.lightImpact();
            onTap();
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Icon(icon, color: color, size: 22),
          ),
        ),
      ),
    );
  }

  // Build advanced smart field - حقل ذكي متطور
  Widget buildCompactClickableField({
    required TextEditingController controller,
    required String hintText,
    required IconData icon,
    required VoidCallback onTap,
    String? Function(String?)? validator,
    required Color iconColor,
    required bool hasValue,
  }) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 400),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // العنوان المتطور مع تأثيرات
                  Padding(
                    padding: const EdgeInsets.only(bottom: 6),
                    child: Row(
                      children: [
                        // أيقونة متحركة مع تدرج
                        TweenAnimationBuilder<double>(
                          duration: const Duration(milliseconds: 600),
                          tween: Tween(begin: 0.0, end: 1.0),
                          curve: Curves.elasticOut,
                          builder: (context, iconValue, _) {
                            return Transform.scale(
                              scale: iconValue,
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      iconColor.withValues(alpha: 0.1),
                                      iconColor.withValues(alpha: 0.2),
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(6),
                                  boxShadow: [
                                    BoxShadow(
                                      color: iconColor.withValues(alpha: 0.2),
                                      blurRadius: 4,
                                      offset: const Offset(1, 1),
                                    ),
                                  ],
                                ),
                                child: Icon(icon, color: iconColor, size: 16),
                              ),
                            );
                          },
                        ),
                        const SizedBox(width: 8),
                        // نص موحد
                        Text(
                          hintText,
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade700,
                          ),
                        ),
                        const Spacer(),
                        // مؤشر الحالة الموحد
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                hasValue
                                    ? Icons.check_circle
                                    : Icons.radio_button_unchecked,
                                size: 12,
                                color: hasValue
                                    ? Colors.green.shade600
                                    : Colors.grey.shade600,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                hasValue ? 'مكتمل' : 'مطلوب',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: hasValue
                                      ? Colors.green.shade600
                                      : Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // الحقل والزر متقابلين مع تأثيرات متطورة
                  Row(
                    children: [
                      // الأزرار الذكية المتطورة - على اليسار
                      Column(
                        children: [
                          // زر الاختيار/التعديل المتطور
                          _buildSmartButton(
                            onTap: onTap,
                            icon: hasValue
                                ? Icons.edit_outlined
                                : Icons.add_circle_outline,
                            color: iconColor,
                            isActive: true,
                            tooltip: hasValue ? 'تعديل' : 'اختيار',
                          ),

                          // زر المسح الذكي (فقط لنوع الكارت عند وجود قيمة)
                          if (hasValue &&
                              icon == Icons.credit_card_outlined) ...[
                            const SizedBox(height: 8),
                            _buildSmartButton(
                              onTap: () {
                                // تأثير اهتزاز قبل المسح
                                HapticFeedback.mediumImpact();
                                setState(() {
                                  controller.clear();
                                  _selectedCardType = null;
                                  _selectedCardTypes.clear();
                                });
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: const Row(
                                      children: [
                                        Icon(
                                          Icons.check_circle,
                                          color: Colors.white,
                                        ),
                                        SizedBox(width: 8),
                                        Text('تم مسح نوع الكارت بنجاح'),
                                      ],
                                    ),
                                    backgroundColor: Colors.green,
                                    duration: const Duration(seconds: 2),
                                    behavior: SnackBarBehavior.floating,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                );
                              },
                              icon: Icons.delete_sweep_outlined,
                              color: Colors.red.shade600,
                              isActive: false,
                              tooltip: 'مسح',
                            ),
                          ],
                        ],
                      ),

                      const SizedBox(width: 12),

                      // الحقل الذكي المتطور - على اليمين
                      Expanded(
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(color: Colors.grey.shade300),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withValues(alpha: 0.1),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: hasValue
                              ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // النص الموحد
                                    Text(
                                      controller.text,
                                      style: const TextStyle(
                                        color: Colors.black87,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        letterSpacing: 0.5,
                                      ),
                                    ),
                                    if (icon == Icons.sticky_note_2_outlined &&
                                        controller.text.isNotEmpty) ...[
                                      const SizedBox(height: 6),
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.info_outline,
                                            size: 12,
                                            color: Colors.grey.shade500,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            'اضغط للتعديل',
                                            style: TextStyle(
                                              color: Colors.grey.shade500,
                                              fontSize: 11,
                                              fontStyle: FontStyle.italic,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ],
                                )
                              : Row(
                                  children: [
                                    // أيقونة متحركة للحالة الفارغة
                                    TweenAnimationBuilder<double>(
                                      duration: const Duration(
                                        milliseconds: 1500,
                                      ),
                                      tween: Tween(begin: 0.0, end: 1.0),
                                      curve: Curves.easeInOut,
                                      builder: (context, pulseValue, _) {
                                        return Transform.scale(
                                          scale: 1.0 +
                                              (0.1 *
                                                  math.sin(
                                                    pulseValue * math.pi * 2,
                                                  )),
                                          child: Icon(
                                            Icons.touch_app_rounded,
                                            color: Colors.grey.shade400,
                                            size: 20,
                                          ),
                                        );
                                      },
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'اضغط للاختيار',
                                            style: TextStyle(
                                              color: Colors.grey.shade600,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          Text(
                                            'تقنية ذكية للاختيار السريع',
                                            style: TextStyle(
                                              color: Colors.grey.shade400,
                                              fontSize: 11,
                                              fontStyle: FontStyle.italic,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Build compact date field - حقل التاريخ المدمج
  Widget buildCompactDateField({
    required String hintText,
    required DateTime date,
    required VoidCallback onTap,
    required IconData icon,
    required Color iconColor,
    bool showTime = false,
  }) {
    final dateFormat = DateFormat('yyyy/MM/dd', 'en');
    final timeFormat = DateFormat('HH:mm', 'en');

    // أسماء الأيام بالعربية
    final List<String> arabicDays = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    final dayName = arabicDays[date.weekday - 1];

    String displayText = dateFormat.format(date);
    if (showTime) {
      displayText += ' - ${timeFormat.format(date)}';
    }
    displayText += ' ($dayName)';

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(icon, color: iconColor, size: 22),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      hintText,
                      style: TextStyle(
                        color: Colors.grey.shade500,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      displayText,
                      style: const TextStyle(
                        color: Colors.black87,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey.shade400,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء شبكة التواريخ الأنيقة
  Widget _buildDateGrid() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.date_range_rounded,
                  color: Colors.blue.shade600,
                  size: 18,
                ),
              ),
              const SizedBox(width: 10),
              Text(
                'التواريخ',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // بطاقات التواريخ - متقابلة ومتكيفة مع المحتوى
          IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // تاريخ القيد
                Flexible(
                  child: _buildDateCard(
                    title: 'تاريخ القيد',
                    date: _entryDate,
                    icon: Icons.today_rounded,
                    color: Colors.blue,
                    showTime: true,
                    onTap: () => _selectDate(context, true),
                  ),
                ),

                const SizedBox(width: 12),

                // تاريخ الاستحقاق
                Flexible(
                  child: _buildDateCard(
                    title: 'تاريخ الاستحقاق',
                    date: _dueDate,
                    icon: Icons.event_available_rounded,
                    color: Colors.orange,
                    showTime: false,
                    onTap: () => _selectDate(context, false),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء بطاقة تاريخ واحدة
  Widget _buildDateCard({
    required String title,
    required DateTime date,
    required IconData icon,
    required Color color,
    required bool showTime,
    required VoidCallback onTap,
  }) {
    // تنسيق التاريخ مع أسماء الأشهر بالأرقام العربية
    String formatDateWithArabicMonth(DateTime date) {
      final arabicMonth = getArabicMonthName(date.month);
      return '${date.day} $arabicMonth ${date.year}';
    }

    final timeFormat12 = DateFormat('hh:mm a', 'en');

    // أسماء الأيام بالعربية
    final List<String> arabicDays = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    final dayName = arabicDays[date.weekday - 1];

    // تحويل AM/PM إلى العربية
    String formatTime(DateTime dateTime) {
      final formatted = timeFormat12.format(dateTime);
      return formatted.replaceAll('AM', 'صباحاً').replaceAll('PM', 'مساءً');
    }

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
          decoration: BoxDecoration(
            // تدرج لوني للخلفية
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.white, Colors.grey.shade50, Colors.grey.shade100],
              stops: const [0.0, 0.7, 1.0],
            ),
            borderRadius: BorderRadius.circular(16),
            // حواف ثلاثية الأبعاد
            border: Border.all(color: Colors.grey.shade300, width: 1.5),
            // ظلال ثلاثية الأبعاد
            boxShadow: [
              // الظل الرئيسي (أسفل ويمين)
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(3, 3),
                spreadRadius: 1,
              ),
              // الظل الداخلي (أعلى ويسار)
              BoxShadow(
                color: Colors.white.withValues(alpha: 0.8),
                blurRadius: 4,
                offset: const Offset(-1, -1),
              ),
              // ظل إضافي للعمق
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 12,
                offset: const Offset(0, 6),
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min, // تكيف مع المحتوى
            children: [
              // الأيقونة والعنوان
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.red.shade600,
                      borderRadius: BorderRadius.circular(4),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.red.withValues(alpha: 0.3),
                          blurRadius: 3,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: double.infinity,
                          height: 4,
                          decoration: BoxDecoration(
                            color: Colors.red.shade800,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(4),
                              topRight: Radius.circular(4),
                            ),
                          ),
                        ),
                        const Expanded(
                          child: Center(
                            child: Text(
                              '17',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 8,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade700,
                        height: 1.2,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // التاريخ واسم اليوم
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // التاريخ الكامل
                  Text(
                    formatDateWithArabicMonth(date),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                      // ظل للنص لإعطاء تأثير ثلاثي الأبعاد
                      shadows: [
                        Shadow(
                          color: Colors.white.withValues(alpha: 0.8),
                          offset: const Offset(1, 1),
                          blurRadius: 2,
                        ),
                        Shadow(
                          color: Colors.grey.withValues(alpha: 0.3),
                          offset: const Offset(-0.5, -0.5),
                          blurRadius: 1,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 2),

                  // اسم اليوم
                  Text(
                    dayName,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w600,
                      // ظل خفيف للنص
                      shadows: [
                        Shadow(
                          color: Colors.white.withValues(alpha: 0.7),
                          offset: const Offset(0.5, 0.5),
                          blurRadius: 1,
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // الوقت (فقط لتاريخ القيد)
              if (showTime) ...[
                const SizedBox(height: 4),
                Text(
                  formatTime(date),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                    // ظل خفيف للوقت
                    shadows: [
                      Shadow(
                        color: Colors.white.withValues(alpha: 0.6),
                        offset: const Offset(0.5, 0.5),
                        blurRadius: 1,
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // Build enhanced date field
  Widget buildEnhancedDateField({
    required String label,
    required DateTime date,
    required VoidCallback onTap,
    required IconData icon,
    required Color color,
    bool showTime = false,
  }) {
    // تنسيق التاريخ مع أسماء الأشهر بالأرقام العربية
    String formatDateWithArabicMonth(DateTime date) {
      final arabicMonth = getArabicMonthName(date.month);
      return '${date.day} $arabicMonth ${date.year}';
    }

    final timeFormat = DateFormat(
      'HH:mm',
      'en',
    ); // تغيير إلى 24 ساعة بدون AM/PM

    // أسماء الأيام بالعربية
    final List<String> arabicDays = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    final dayName = arabicDays[date.weekday - 1];

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.grey.shade50, // أبيض مائل للرمادي
          border: Border.all(color: Colors.black87), // حواف سوداء بسيطة
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 14,
                      color: color,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    showTime
                        ? '$dayName - ${formatDateWithArabicMonth(date)} ${timeFormat.format(date)}'
                        : '$dayName - ${formatDateWithArabicMonth(date)}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.edit_calendar, color: color, size: 20),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, (1 - _animation.value) * 30),
          child: Opacity(
            opacity: _animation.value,
            child: Scaffold(
              backgroundColor: Colors.transparent,
              body: Container(
                height: MediaQuery.of(context).size.height * 0.95,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.blue.shade50,
                      Colors.white,
                      Colors.grey.shade50,
                    ],
                    stops: const [0.0, 0.3, 1.0],
                  ),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(30),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.15),
                      blurRadius: 30,
                      offset: const Offset(0, -10),
                      spreadRadius: 5,
                    ),
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, -5),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Enhanced Handle with gradient
                    Container(
                      margin: const EdgeInsets.only(top: 16),
                      width: 60,
                      height: 6,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue.shade300, Colors.blue.shade500],
                        ),
                        borderRadius: BorderRadius.circular(3),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withValues(alpha: 0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                    ),

                    // شريط علوي بسيط ونظيف
                    Container(
                      padding: const EdgeInsets.fromLTRB(20, 16, 20, 20),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.blue.shade600,
                            Colors.blue.shade800,
                            Colors.indigo.shade700,
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withValues(alpha: 0.3),
                            blurRadius: 15,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // الصف العلوي - العنوان وزر الإغلاق
                          Row(
                            children: [
                              // زر الإغلاق
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: IconButton(
                                  onPressed: () {
                                    _saveFormData();
                                    Navigator.of(context).pop();
                                  },
                                  icon: const Icon(
                                    Icons.close,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                              ),

                              const SizedBox(width: 16),

                              // العنوان
                              const Expanded(
                                child: Text(
                                  'إضافة دين جديد',
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ),

                              // قائمة الإعدادات
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: PopupMenuButton<String>(
                                  icon: const Icon(
                                    Icons.more_vert,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  onSelected: (value) {
                                    switch (value) {
                                      case 'clear':
                                        clearForm();
                                        break;
                                      case 'reset_quick':
                                        resetQuickValues();
                                        break;
                                    }
                                  },
                                  itemBuilder: (context) => [
                                    PopupMenuItem(
                                      value: 'clear',
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.clear_all,
                                            size: 18,
                                            color: Colors.red.shade600,
                                          ),
                                          const SizedBox(width: 12),
                                          const Text('مسح النموذج'),
                                        ],
                                      ),
                                    ),
                                    PopupMenuItem(
                                      value: 'reset_quick',
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.refresh,
                                            size: 18,
                                            color: Colors.blue.shade600,
                                          ),
                                          const SizedBox(width: 12),
                                          const Text('إعادة تعيين القيم'),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // مؤشر التقدم
                          buildProgressIndicator(),
                        ],
                      ),
                    ),

                    // Enhanced Form with better spacing and design
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          // إغلاق قائمة أنواع الكروت عند النقر خارجها
                          _closeCardTypeDropdown();
                        },
                        child: SingleChildScrollView(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 16),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // بطاقة الحقول الأساسية
                                Container(
                                  padding: const EdgeInsets.all(20),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                    border:
                                        Border.all(color: Colors.grey.shade200),
                                    boxShadow: [
                                      BoxShadow(
                                        color:
                                            Colors.grey.withValues(alpha: 0.08),
                                        blurRadius: 10,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // عنوان القسم
                                      Row(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(8),
                                            decoration: BoxDecoration(
                                              color: Colors.blue.shade50,
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            child: Icon(
                                              Icons.edit_document,
                                              color: Colors.blue.shade600,
                                              size: 20,
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          Text(
                                            'بيانات الدين الأساسية',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.grey.shade800,
                                            ),
                                          ),
                                        ],
                                      ),

                                      const SizedBox(height: 20),

                                      // Customer Section - العميل
                                      _buildModernField(
                                        controller: _customerNameController,
                                        label: 'العميل',
                                        icon: Icons.person_outline,
                                        iconColor: Colors.blue.shade600,
                                        onTap: () {
                                          _closeCardTypeDropdown();
                                          _showCustomerSelectionDialog();
                                        },
                                        hasValue: _selectedCustomer != null,
                                        isRequired: true,
                                        showDeleteButton:
                                            _selectedCustomer != null,
                                        onDelete: () {
                                          setState(() {
                                            _selectedCustomer = null;
                                            _customerNameController.clear();
                                          });
                                        },
                                      ),

                                      const SizedBox(height: 16),

                                      // Card Type Section - نوع الكارت
                                      _buildModernField(
                                        controller: _cardTypeController,
                                        label: 'أنواع الكروت',
                                        icon: Icons.credit_card_outlined,
                                        iconColor: Colors.purple.shade600,
                                        onTap: () {
                                          _showCardTypeManagementDialog();
                                        },
                                        hasValue: _selectedCardTypes.isNotEmpty,
                                        isRequired: true,
                                        displayText: _selectedCardTypes.isEmpty
                                            ? null
                                            : _selectedCardTypes.length == 1
                                                ? _selectedCardTypes
                                                    .first['name']
                                                : '${_selectedCardTypes.length} كارت: ${_selectedCardTypes.map((c) => c['name']).join(', ')}',
                                        showDeleteButton:
                                            _selectedCardTypes.isNotEmpty,
                                        onDelete: () {
                                          setState(() {
                                            _selectedCardTypes.clear();
                                            _cardTypeController.clear();
                                          });
                                        },
                                      ),

                                      const SizedBox(height: 16),

                                      // Notes Section - الملاحظات
                                      _buildModernField(
                                        controller: _notesController,
                                        label: 'الملاحظات (اختياري)',
                                        icon: Icons.sticky_note_2_outlined,
                                        iconColor: Colors.orange.shade600,
                                        onTap: () {
                                          _closeCardTypeDropdown();
                                          _showNotesDialog();
                                        },
                                        hasValue:
                                            _notesController.text.isNotEmpty,
                                        isRequired: false,
                                        showDeleteButton:
                                            _notesController.text.isNotEmpty,
                                        onDelete: () {
                                          setState(() {
                                            _notesController.clear();
                                          });
                                        },
                                      ),
                                    ],
                                  ),
                                ),

                                const SizedBox(height: 12),

                                // شبكة التواريخ الأنيقة
                                _buildDateGrid(),

                                // قسم دمج الكارتات تحت التواريخ عند اختيار كارتات
                                if (_selectedCardTypes.isNotEmpty) ...[
                                  const SizedBox(height: 8),
                                  _buildMergeCardsSection(),
                                ],

                                // عرض الكارتات المحددة تلقائياً (تحت قسم الدمج)
                                if (_selectedCardTypes.isNotEmpty) ...[
                                  const SizedBox(height: 8),
                                  _buildSelectedCardsDisplay(),
                                ],

                                const SizedBox(height: 16),

                                // زر الحفظ المحسن
                                Container(
                                  width: double.infinity,
                                  margin:
                                      const EdgeInsets.symmetric(horizontal: 4),
                                  child: _buildModernSaveButton(),
                                ),

                                const SizedBox(height: 20),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // مؤشر التقدم
  Widget buildProgressIndicator() {
    int completedSteps = 0;
    const int totalSteps = 3; // العميل، الكارتات، التواريخ

    if (_selectedCustomer != null) completedSteps++;

    // التحقق من الكارتات (نظام الكارتات المتعددة فقط)
    if (_selectedCardTypes.isNotEmpty) completedSteps++;

    // التواريخ دائماً موجودة (لها قيم افتراضية)
    completedSteps++;

    final progress = completedSteps / totalSteps;

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: progress,
                backgroundColor: Colors.white.withValues(alpha: 0.3),
                valueColor: AlwaysStoppedAnimation<Color>(
                  progress == 1.0 ? Colors.green.shade300 : Colors.white,
                ),
                minHeight: 6,
              ),
            ),
            const SizedBox(width: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '$completedSteps/$totalSteps',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          progress == 1.0
              ? '✅ جميع البيانات مكتملة - جاهز للحفظ!'
              : 'أكمل البيانات المطلوبة (${(progress * 100).toInt()}%)',
          style: TextStyle(
            fontSize: 11,
            color: progress == 1.0
                ? Colors.green.shade200
                : Colors.white.withValues(alpha: 0.8),
            fontWeight: progress == 1.0 ? FontWeight.w600 : FontWeight.normal,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // التحقق من اكتمال النموذج
  bool isFormComplete() {
    if (_selectedCustomer == null) return false;

    // الآن نعتمد فقط على نظام الكارتات المتعددة
    return _selectedCardTypes.isNotEmpty;
  }

  String getCardTypeName() {
    if (_selectedCardType == null) return '';

    final cardTypeProvider = Provider.of<CardTypeProvider>(
      context,
      listen: false,
    );

    if (_selectedCardType!.startsWith('custom_')) {
      final cardTypeId = int.parse(
        _selectedCardType!.replaceFirst('custom_', ''),
      );
      final customCardType = cardTypeProvider.customCardTypes.firstWhere(
        (ct) => ct.id == cardTypeId,
        orElse: () => throw Exception('Card type not found'),
      );
      return customCardType.displayName;
    }

    return _selectedCardType!;
  }

  // التحقق من السقف الائتماني
  Future<bool> _checkCreditLimit() async {
    if (_selectedCustomer == null) return true;

    // التحقق من وجود سقف للعميل
    if (_selectedCustomer!.creditLimit == null ||
        _selectedCustomer!.creditLimit! <= 0) {
      return true; // لا يوجد سقف، السماح بالإضافة
    }

    // حساب إجمالي مبلغ الدين الجديد
    final totalNewDebtAmount = _selectedCardTypes.fold<double>(
      0.0,
      (sum, card) => sum + (card['quantity'] * card['price']),
    );

    // الحصول على الديون الحالية للعميل
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    final customerDebts = debtProvider.debts
        .where((debt) => debt.customerId == _selectedCustomer!.id)
        .toList();

    // حساب إجمالي الديون المتبقية
    final currentDebt = customerDebts.fold<double>(
      0.0,
      (sum, debt) => sum + debt.remainingAmount,
    );

    // حساب إجمالي الديون بعد إضافة الدين الجديد
    final totalDebtAfterAddition = currentDebt + totalNewDebtAmount;

    // التحقق من تجاوز السقف
    if (totalDebtAfterAddition > _selectedCustomer!.creditLimit!) {
      final exceededAmount =
          totalDebtAfterAddition - _selectedCustomer!.creditLimit!;

      // إظهار رسالة تحذير مع خيار السماح
      final bool? allowOverride = await showDialog<bool>(
        context: context,
        barrierDismissible: false, // منع الإغلاق بالنقر خارج النافذة
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.red.shade600, size: 28),
              const SizedBox(width: 12),
              const Text(
                'تجاوز السقف الائتماني',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          content: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.red.shade200),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'سيتم تجاوز السقف الائتماني للعميل ${_selectedCustomer!.name}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.shade300),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.orange.shade700,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'يمكنك السماح بالتجاوز أو إلغاء العملية',
                          style: TextStyle(
                            color: Colors.orange.shade700,
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                _buildLimitInfoRow(
                  'السقف الائتماني:',
                  NumberFormat(
                    '#,##0.00',
                  ).format(_selectedCustomer!.creditLimit!),
                  Colors.blue.shade600,
                ),
                const SizedBox(height: 8),
                _buildLimitInfoRow(
                  'الديون الحالية:',
                  NumberFormat('#,##0.00').format(currentDebt),
                  Colors.orange.shade600,
                ),
                const SizedBox(height: 8),
                _buildLimitInfoRow(
                  'الدين الجديد:',
                  NumberFormat('#,##0.00').format(totalNewDebtAmount),
                  Colors.purple.shade600,
                ),
                const Divider(height: 20),
                _buildLimitInfoRow(
                  'الإجمالي بعد الإضافة:',
                  NumberFormat('#,##0.00').format(totalDebtAfterAddition),
                  Colors.red.shade600,
                ),
                const SizedBox(height: 8),
                _buildLimitInfoRow(
                  'المبلغ المتجاوز:',
                  NumberFormat('#,##0.00').format(exceededAmount),
                  Colors.red.shade700,
                ),
              ],
            ),
          ),
          actions: [
            // زر الإلغاء
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text(
                'إلغاء',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(width: 8),
            // زر السماح بالتجاوز
            ElevatedButton.icon(
              onPressed: () => Navigator.pop(context, true),
              icon: const Icon(Icons.warning_amber, size: 18),
              label: const Text('السماح بالتجاوز'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      );

      // التعامل مع نتيجة النافذة
      if (allowOverride == true) {
        // المستخدم اختار السماح بالتجاوز
        return true;
      } else {
        // المستخدم اختار الإلغاء أو أغلق النافذة
        return false;
      }
    }

    return true; // السماح بالإضافة
  }

  Widget _buildLimitInfoRow(String label, String value, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade700,
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}

// نافذة اختيار العميل المحسنة
class _CustomerSelectionBottomSheet extends StatefulWidget {
  const _CustomerSelectionBottomSheet({required this.onCustomerSelected});

  final Function(Customer) onCustomerSelected;

  @override
  State<_CustomerSelectionBottomSheet> createState() =>
      _CustomerSelectionBottomSheetState();
}

class _CustomerSelectionBottomSheetState
    extends State<_CustomerSelectionBottomSheet> with TickerProviderStateMixin {
  final TextEditingController searchController = TextEditingController();
  final ScrollController scrollController = ScrollController();
  final FocusNode searchFocusNode = FocusNode();

  late AnimationController animationController;
  late AnimationController searchAnimationController;
  late AnimationController listAnimationController;

  late Animation<double> animation;
  late Animation<double> searchAnimation;
  late Animation<Offset> slideAnimation;
  late Animation<double> fadeAnimation;

  List<Customer> filteredCustomers = [];
  List<Customer> allCustomers = [];
  String searchQuery = '';
  bool isLoading = true;
  bool isSearchFocused = false;
  String selectedFilter = 'الكل';

  // إحصائيات البحث
  int totalCustomers = 0;
  int customersWithPhone = 0;
  int recentCustomers = 0;

  // Debouncing للبحث السريع
  Timer? _searchDebounceTimer;

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة - سريعة
    animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    listAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    animation = CurvedAnimation(
      parent: animationController,
      curve: Curves.fastOutSlowIn,
    );

    searchAnimation = CurvedAnimation(
      parent: searchAnimationController,
      curve: Curves.fastOutSlowIn,
    );

    slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
      CurvedAnimation(
        parent: listAnimationController,
        curve: Curves.fastOutSlowIn,
      ),
    );

    fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: listAnimationController,
        curve: Curves.fastOutSlowIn,
      ),
    );

    // بدء الرسوم المتحركة
    animationController.forward();

    // إعداد مستمع البحث مع تأخير للأداء
    searchController.addListener(_onSearchChanged);

    // إعداد مستمع التركيز
    searchFocusNode.addListener(_onFocusChanged);

    // تأخير تحميل العملاء لتجنب استدعاء setState أثناء البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      loadCustomers();
    });
  }

  void _onSearchChanged() {
    // إلغاء المؤقت السابق إن وجد
    _searchDebounceTimer?.cancel();

    // إنشاء مؤقت جديد مع تأخير قصير لتحسين الأداء
    _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (mounted) {
        setState(() {
          searchQuery = searchController.text;
          filterCustomers();
        });
      }
    });
  }

  void _onFocusChanged() {
    setState(() {
      isSearchFocused = searchFocusNode.hasFocus;
    });

    if (isSearchFocused) {
      searchAnimationController.forward();
    } else {
      searchAnimationController.reverse();
    }
  }

  @override
  void dispose() {
    // تنظيف مؤقت البحث
    _searchDebounceTimer?.cancel();

    animationController.dispose();
    searchAnimationController.dispose();
    listAnimationController.dispose();
    searchController.dispose();
    scrollController.dispose();
    searchFocusNode.dispose();
    super.dispose();
  }

  Future<void> loadCustomers() async {
    try {
      // تحميل العملاء مباشرة من قاعدة البيانات بدون Provider
      final databaseHelper = DatabaseHelper();

      // تحسين الأداء: تحميل العملاء بشكل مجمع
      final allCustomersFromDb = await databaseHelper.getAllCustomers();

      // استبعاد العميل الوهمي "ديون مستقلة" من قائمة العملاء
      final customers = allCustomersFromDb
          .where((customer) => customer.name != 'ديون مستقلة')
          .toList();

      // ترتيب العملاء أبجدياً لتحسين البحث
      customers.sort((a, b) => a.name.compareTo(b.name));

      // التأكد من أن الـ widget ما زال mounted قبل setState
      if (mounted) {
        setState(() {
          allCustomers = customers;
          filteredCustomers = customers;
          isLoading = false;

          // حساب الإحصائيات
          _calculateStatistics();
        });

        // بدء رسوم متحركة للقائمة بشكل سريع
        listAnimationController.forward();
      }
    } catch (e) {
      debugPrint('خطأ في تحميل العملاء: $e');
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  void _calculateStatistics() {
    totalCustomers = allCustomers.length;
    customersWithPhone = allCustomers.where((c) => c.phone != null).length;

    // العملاء الجدد (آخر 7 أيام)
    final weekAgo = DateTime.now().subtract(const Duration(days: 7));
    recentCustomers =
        allCustomers.where((c) => c.createdAt.isAfter(weekAgo)).length;
  }

  void filterCustomers() {
    List<Customer> baseList = allCustomers;

    // تطبيق البحث النصي أولاً - محسن للأداء
    if (searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase().trim();

      // تحسين الأداء: البحث السريع أولاً
      baseList = allCustomers.where((customer) {
        final name = customer.name.toLowerCase();
        final phone = customer.phone?.toLowerCase() ?? '';

        // البحث السريع أولاً (الأكثر شيوعاً)
        if (name.contains(query) || phone.contains(query)) {
          return true;
        }

        // البحث المتقدم فقط إذا لم نجد مطابقة سريعة
        return _isAdvancedMatch(name, query) ||
            isPhoneNumberMatch(phone, query) ||
            isNameMatch(name, query);
      }).toList();

      // ترتيب النتائج حسب الصلة - محسن
      baseList.sort((a, b) {
        final aName = a.name.toLowerCase();
        final bName = b.name.toLowerCase();

        // الأولوية للمطابقة في البداية
        final aStartsWith = aName.startsWith(query);
        final bStartsWith = bName.startsWith(query);

        if (aStartsWith && !bStartsWith) return -1;
        if (!aStartsWith && bStartsWith) return 1;

        // ثم الترتيب الأبجدي
        return aName.compareTo(bName);
      });
    }

    // تطبيق الفلاتر السريعة
    switch (selectedFilter) {
      case 'لديه هاتف':
        filteredCustomers = baseList.where((c) => c.phone != null).toList();
        break;
      case 'جديد':
        final weekAgo = DateTime.now().subtract(const Duration(days: 7));
        filteredCustomers =
            baseList.where((c) => c.createdAt.isAfter(weekAgo)).toList();
        break;
      case 'نشط':
        // يمكن إضافة منطق للعملاء النشطين هنا
        filteredCustomers = baseList;
        break;
      default:
        filteredCustomers = baseList;
    }
  }

  bool _isAdvancedMatch(String text, String query) {
    if (query.isEmpty) return false;

    // تنظيف النصوص من الرموز والمسافات الزائدة
    final cleanText = _cleanArabicText(text);
    final cleanQuery = _cleanArabicText(query);

    // البحث العادي
    if (cleanText.contains(cleanQuery)) return true;

    // البحث بالكلمات المنفصلة
    final words = cleanQuery.split(' ').where((w) => w.isNotEmpty).toList();
    if (words.length > 1) {
      return words.every((word) => cleanText.contains(word));
    }

    // البحث بالأحرف المتتالية (مثل "احمد" يجد "أحمد علي")
    if (cleanQuery.length >= 2) {
      return _isSequentialMatch(cleanText, cleanQuery);
    }

    // البحث بالحرف الأول
    if (cleanQuery.length == 1) {
      return cleanText.startsWith(cleanQuery);
    }

    return false;
  }

  String _cleanArabicText(String text) {
    return text
        .toLowerCase()
        .replaceAll(RegExp(r'[أإآ]'), 'ا') // توحيد الألف
        .replaceAll(RegExp(r'[ىي]'), 'ي') // توحيد الياء
        .replaceAll(RegExp(r'[ةه]'), 'ه') // توحيد التاء المربوطة والهاء
        .replaceAll(RegExp(r'[ؤئ]'), 'و') // توحيد الواو والهمزة
        .replaceAll(
          RegExp(r'[^\u0600-\u06FF\u0750-\u077F\w\s]'),
          '',
        ) // إزالة الرموز
        .replaceAll(RegExp(r'\s+'), ' ') // توحيد المسافات
        .trim();
  }

  bool _isSequentialMatch(String text, String query) {
    if (query.isEmpty) return false;

    int queryIndex = 0;
    bool wordBoundary = true; // للتأكد من بداية الكلمات

    for (int i = 0; i < text.length && queryIndex < query.length; i++) {
      final char = text[i];

      // إذا كان الحرف الحالي يطابق الحرف المطلوب
      if (char == query[queryIndex]) {
        // إذا كنا في بداية كلمة أو الحرف السابق كان مطابقاً
        if (wordBoundary || queryIndex > 0) {
          queryIndex++;
          wordBoundary = false;
        }
      } else if (char == ' ') {
        wordBoundary = true;
      } else {
        wordBoundary = false;
      }
    }

    return queryIndex == query.length;
  }

  double _calculateSimilarity(String s1, String s2) {
    if (s1.isEmpty || s2.isEmpty) return 0.0;

    final longer = s1.length > s2.length ? s1 : s2;
    final shorter = s1.length > s2.length ? s2 : s1;

    if (longer.isEmpty) return 1.0;

    return (longer.length - _levenshteinDistance(longer, shorter)) /
        longer.length;
  }

  int _levenshteinDistance(String s1, String s2) {
    final matrix = List.generate(
      s1.length + 1,
      (i) => List.generate(s2.length + 1, (j) => 0),
    );

    for (int i = 0; i <= s1.length; i++) {
      matrix[i][0] = i;
    }
    for (int j = 0; j <= s2.length; j++) {
      matrix[0][j] = j;
    }

    for (int i = 1; i <= s1.length; i++) {
      for (int j = 1; j <= s2.length; j++) {
        final cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        ].reduce((a, b) => a < b ? a : b);
      }
    }

    return matrix[s1.length][s2.length];
  }

  bool isPhoneNumberMatch(String phone, String query) {
    // إزالة الرموز والمسافات من رقم الهاتف
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
    final cleanQuery = query.replaceAll(RegExp(r'[^\d]'), '');

    if (cleanQuery.isEmpty) return false;

    return cleanPhone.contains(cleanQuery);
  }

  bool isNameMatch(String name, String query) {
    // البحث في كلمات منفصلة
    final nameWords = name.split(' ');
    final queryWords = query.split(' ');

    for (final queryWord in queryWords) {
      if (queryWord.trim().isEmpty) continue;

      bool found = false;
      for (final nameWord in nameWords) {
        if (nameWord.toLowerCase().contains(queryWord.toLowerCase())) {
          found = true;
          break;
        }
      }
      if (!found) return false;
    }

    return queryWords.isNotEmpty;
  }

  void showAddCustomerDialog() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddCustomerScreen(
          onCustomerAdded: (customer) {
            // تحديث قائمة العملاء
            loadCustomers();
            // اختيار العميل الجديد تلقائياً
            widget.onCustomerSelected(customer);
            // لا نغلق النافذة هنا - دع AddCustomerScreen يتحكم في ذلك
          },
        ),
      ),
    );
  }

  Widget buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.person_search, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            searchQuery.isEmpty ? 'لا توجد عملاء' : 'لم يتم العثور على عملاء',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            searchQuery.isEmpty
                ? 'أضف عميل جديد للبدء'
                : 'جرب البحث بكلمات مختلفة',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  Widget buildCustomerItem(Customer customer, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue.shade400, Colors.blue.shade600],
            ),
            borderRadius: BorderRadius.circular(25),
          ),
          child: Center(
            child: Text(
              customer.name.isNotEmpty ? customer.name[0].toUpperCase() : '؟',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        title: Text(
          customer.name,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        subtitle: customer.phone != null
            ? Row(
                children: [
                  Icon(Icons.phone, size: 16, color: Colors.grey.shade600),
                  const SizedBox(width: 4),
                  Text(
                    customer.phone!,
                    style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                  ),
                ],
              )
            : null,
        trailing: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green.shade200),
          ),
          child: Icon(Icons.check, color: Colors.green.shade600, size: 20),
        ),
        onTap: () {
          widget.onCustomerSelected(customer);
          Navigator.of(context).pop();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, (1 - animation.value) * 30),
          child: Opacity(
            opacity: animation.value,
            child: Container(
              height: MediaQuery.of(context).size.height * 0.9,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
              ),
              child: Column(
                children: [
                  // Handle
                  Container(
                    margin: const EdgeInsets.only(top: 12),
                    width: 50,
                    height: 5,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),

                  // Header
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        bottom: BorderSide(color: Colors.grey.shade200),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: Icon(
                              Icons.close,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ),
                        Expanded(
                          child: Column(
                            children: [
                              Text(
                                'اختيار العميل',
                                style: Theme.of(context)
                                    .textTheme
                                    .headlineSmall
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'اختر عميل من القائمة أو أضف عميل جديد',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                        // أزرار الإجراءات
                        Row(
                          children: [
                            // زر إضافة عميل جديد
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.green.shade50,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.green.shade200,
                                ),
                              ),
                              child: IconButton(
                                onPressed: showAddCustomerDialog,
                                icon: Icon(
                                  Icons.person_add,
                                  color: Colors.green.shade700,
                                ),
                                tooltip: 'إضافة عميل جديد',
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Professional Search Section
                  _buildSearchSection(),

                  // Content
                  Expanded(
                    child: isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : filteredCustomers.isEmpty
                            ? buildEmptyState()
                            : ListView.builder(
                                controller: scrollController,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                itemCount: filteredCustomers.length,
                                // تحسين أداء القائمة
                                addAutomaticKeepAlives: false,
                                addSemanticIndexes: false,
                                // تحسين الذاكرة
                                cacheExtent: 400.0,
                                itemBuilder: (context, index) {
                                  final customer = filteredCustomers[index];
                                  return RepaintBoundary(
                                    child: buildCustomerItem(customer, index),
                                  );
                                },
                              ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Professional Search Section Widget
  Widget _buildSearchSection() {
    return AnimatedBuilder(
      animation: searchAnimation,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Statistics Row
              if (totalCustomers > 0) _buildStatisticsRow(),

              if (totalCustomers > 0) const SizedBox(height: 16),

              // Enhanced Search Field
              _buildEnhancedSearchField(),

              // Quick Filters
              if (searchQuery.isNotEmpty) ...[
                const SizedBox(height: 12),
                _buildQuickFilters(),
              ],

              // Search Suggestions
              if (searchQuery.isNotEmpty && searchQuery.length >= 2) ...[
                const SizedBox(height: 8),
                _buildSearchSuggestions(),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatisticsRow() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue.shade50, Colors.purple.shade50],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade100),
      ),
      child: Row(
        children: [
          _buildStatCard(
            icon: Icons.people_rounded,
            label: 'إجمالي العملاء',
            value: totalCustomers.toString(),
            color: Colors.blue,
          ),
          const SizedBox(width: 12),
          _buildStatCard(
            icon: Icons.phone_rounded,
            label: 'لديهم هاتف',
            value: customersWithPhone.toString(),
            color: Colors.green,
          ),
          const SizedBox(width: 12),
          _buildStatCard(
            icon: Icons.new_releases_rounded,
            label: 'جدد',
            value: recentCustomers.toString(),
            color: Colors.orange,
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.2)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              label,
              style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedSearchField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isSearchFocused
                ? Colors.blue.withValues(alpha: 0.2)
                : Colors.grey.withValues(alpha: 0.1),
            blurRadius: isSearchFocused ? 8 : 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: searchController,
        focusNode: searchFocusNode,
        style: const TextStyle(
          color: Colors.black87,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          hintText: 'ابحث عن عميل بالاسم أو رقم الهاتف...',
          hintStyle: TextStyle(color: Colors.grey.shade500, fontSize: 14),
          prefixIcon: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            margin: const EdgeInsets.all(8),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isSearchFocused
                    ? [Colors.blue.shade400, Colors.purple.shade400]
                    : [Colors.grey.shade200, Colors.grey.shade300],
              ),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              Icons.search_rounded,
              color: isSearchFocused ? Colors.white : Colors.grey.shade600,
              size: 20,
            ),
          ),
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (searchQuery.isNotEmpty) ...[
                // Results Counter
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.blue.shade100, Colors.purple.shade100],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.person_search_rounded,
                        size: 14,
                        color: Colors.blue.shade700,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${filteredCustomers.length}',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
                // Clear Button
                Container(
                  margin: const EdgeInsets.only(right: 4),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    onPressed: () {
                      searchController.clear();
                      searchFocusNode.unfocus();
                    },
                    icon: Icon(
                      Icons.clear_rounded,
                      color: Colors.red.shade600,
                      size: 18,
                    ),
                    tooltip: 'مسح البحث',
                  ),
                ),
              ] else ...[
                // Voice Search Button (placeholder)
                Container(
                  margin: const EdgeInsets.only(right: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    onPressed: () {
                      // Voice search functionality
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text('البحث الصوتي قريباً'),
                          backgroundColor: Colors.green.shade600,
                        ),
                      );
                    },
                    icon: Icon(
                      Icons.mic_rounded,
                      color: Colors.green.shade600,
                      size: 18,
                    ),
                    tooltip: 'البحث الصوتي',
                  ),
                ),
                // Help Button
                Container(
                  margin: const EdgeInsets.only(right: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    onPressed: showSearchTips,
                    icon: Icon(
                      Icons.help_outline_rounded,
                      color: Colors.orange.shade600,
                      size: 18,
                    ),
                    tooltip: 'نصائح البحث',
                  ),
                ),
              ],
            ],
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: Colors.blue.shade400, width: 2),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 14,
          ),
        ),
      ),
    );
  }

  Widget _buildQuickFilters() {
    return SizedBox(
      height: 40,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip(
            'الكل',
            filteredCustomers.length,
            selectedFilter == 'الكل',
            () => setState(() => selectedFilter = 'الكل'),
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            'لديه هاتف',
            filteredCustomers.where((c) => c.phone != null).length,
            selectedFilter == 'لديه هاتف',
            () => setState(() => selectedFilter = 'لديه هاتف'),
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            'جديد',
            recentCustomers,
            selectedFilter == 'جديد',
            () => setState(() => selectedFilter = 'جديد'),
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            'نشط',
            filteredCustomers.length,
            selectedFilter == 'نشط',
            () => setState(() => selectedFilter = 'نشط'),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    String label,
    int count,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [Colors.blue.shade400, Colors.purple.shade400],
                )
              : null,
          color: isSelected ? null : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? Colors.transparent : Colors.grey.shade300,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Colors.blue.withValues(alpha: 0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                color: isSelected ? Colors.white : Colors.grey.shade700,
              ),
            ),
            const SizedBox(width: 6),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: isSelected
                    ? Colors.white.withValues(alpha: 0.3)
                    : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                count.toString(),
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: isSelected ? Colors.white : Colors.grey.shade600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchSuggestions() {
    // Get search suggestions based on current query
    final suggestions = _getSearchSuggestions();

    if (suggestions.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.auto_awesome_rounded,
                size: 16,
                color: Colors.blue.shade600,
              ),
              const SizedBox(width: 6),
              Text(
                'اقتراحات البحث:',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Wrap(
            spacing: 6,
            runSpacing: 4,
            children: suggestions.map((suggestion) {
              return GestureDetector(
                onTap: () {
                  searchController.text = suggestion;
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade300),
                  ),
                  child: Text(
                    suggestion,
                    style: TextStyle(fontSize: 11, color: Colors.blue.shade700),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  List<String> _getSearchSuggestions() {
    if (searchQuery.length < 2) return [];

    final suggestions = <String>{};

    // Add name suggestions
    for (final customer in allCustomers) {
      final name = customer.name.toLowerCase();
      final query = searchQuery.toLowerCase();

      if (name.contains(query)) {
        // Add full name
        suggestions.add(customer.name);

        // Add individual words
        final words = customer.name.split(' ');
        for (final word in words) {
          if (word.toLowerCase().startsWith(query)) {
            suggestions.add(word);
          }
        }
      }
    }

    return suggestions.take(5).toList();
  }

  void showSearchTips() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.lightbulb,
                color: Colors.blue.shade600,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text('نصائح البحث'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '💡 يمكنك البحث بـ:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('• الاسم الكامل أو جزء منه'),
            Text('• رقم الهاتف أو جزء منه'),
            Text('• كلمات منفصلة من الاسم'),
            SizedBox(height: 12),
            Text('🔍 أمثلة:', style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            Text('• "أحمد" - للبحث عن جميع الأحمد'),
            Text('• "077" - للبحث برقم الهاتف'),
            Text('• "أحمد علي" - للبحث بالاسم الكامل'),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }
}
