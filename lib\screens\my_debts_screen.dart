import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/debt.dart';
import '../models/customer.dart';
import '../providers/debt_provider.dart';
import '../database/database_helper.dart';

class MyDebtsScreen extends StatefulWidget {
  const MyDebtsScreen({super.key});

  @override
  State<MyDebtsScreen> createState() => _MyDebtsScreenState();
}

class _MyDebtsScreenState extends State<MyDebtsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadMyDebts();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadMyDebts() async {
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    await debtProvider.loadMyDebts();
  }

  List<Debt> _filterDebts(List<Debt> debts) {
    if (_searchQuery.isEmpty) {
      return debts;
    }

    return debts.where((debt) {
      // البحث في اسم الدائن
      String creditorName = 'دين عليه';
      if (debt.notes != null && debt.notes!.contains('دائن:')) {
        final lines = debt.notes!.split('\n');
        for (final line in lines) {
          if (line.startsWith('دائن:')) {
            creditorName = line.replaceFirst('دائن:', '').trim();
            break;
          }
        }
      }

      // البحث في المبلغ
      final amountText = debt.amount.toString();

      // البحث في الملاحظات
      final notes = debt.notes ?? '';

      return creditorName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          amountText.contains(_searchQuery) ||
          notes.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'ديون عليه',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.teal.shade600,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.add_circle_outline),
            onPressed: () => _showAddDebtDialog(),
          ),
        ],
      ),
      body: Consumer<DebtProvider>(
        builder: (context, debtProvider, child) {
          final myDebts = debtProvider.myDebts;
          final filteredDebts = _filterDebts(myDebts);

          return Column(
            children: [
              // شريط البحث
              Container(
                margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TextField(
                  controller: _searchController,
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 16,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                  decoration: InputDecoration(
                    hintText: 'البحث في الديون...',
                    hintStyle: TextStyle(color: Colors.grey.shade500),
                    prefixIcon: Icon(Icons.search, color: Colors.teal.shade600),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon:
                                Icon(Icons.clear, color: Colors.grey.shade600),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                ),
              ),

              if (myDebts.isEmpty)
                Expanded(child: _buildEmptyState())
              else if (filteredDebts.isEmpty)
                Expanded(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد نتائج للبحث',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'جرب البحث بكلمات مختلفة',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              else ...[
                // إحصائيات سريعة
                _buildQuickStats(filteredDebts),

                // قائمة الديون
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: filteredDebts.length,
                    itemBuilder: (context, index) {
                      final debt = filteredDebts[index];
                      return _buildDebtCard(debt);
                    },
                  ),
                ),
              ],
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.account_balance_wallet_outlined,
              size: 64,
              color: Colors.red.shade300,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد ديون عليك',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم تقم بتسجيل أي ديون عليك بعد',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () => _showAddDebtDialog(),
            icon: const Icon(Icons.add),
            label: const Text('إضافة دين جديد'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(List<Debt> debts) {
    final totalAmount = debts.fold<double>(0, (sum, debt) => sum + debt.amount);
    final totalCount = debts.length;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إجمالي الديون',
                  style: TextStyle(
                    color: Colors.blue.shade900,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${NumberFormat('#,###.000', 'en').format(totalAmount)} ألف',
                  style: TextStyle(
                    color: Colors.blue.shade900,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Container(
            width: 1,
            height: 40,
            color: Colors.grey.shade300,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'عدد الديون',
                  style: TextStyle(
                    color: Colors.blue.shade900,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '$totalCount دين',
                  style: TextStyle(
                    color: Colors.blue.shade900,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // دالة حساب الوقت منذ التسديد
  String _getTimeSincePayment(DateTime paymentDate) {
    final now = DateTime.now();
    final difference = now.difference(paymentDate);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'منذ لحظات';
    }
  }

  // دالة لتنظيف الملاحظات من "دائن:"
  String _getCleanNotes(String notes) {
    final lines = notes.split('\n');
    final cleanLines = <String>[];

    for (final line in lines) {
      if (!line.trim().startsWith('دائن:')) {
        cleanLines.add(line);
      }
    }

    return cleanLines.join('\n').trim();
  }

  // دالة التراجع عن التسديد
  void _undoPayment(Debt debt) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        title: Row(
          children: [
            Icon(Icons.undo, color: Colors.orange.shade600, size: 24),
            const SizedBox(width: 8),
            Text(
              'التراجع عن التسديد',
              style: TextStyle(
                color: Colors.orange.shade700,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.warning,
              color: Colors.orange.shade600,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'هل أنت متأكد من التراجع عن تسديد هذا الدين؟',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade800,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'سيتم إرجاع الدين إلى حالة غير مدفوع',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey.shade700,
            ),
            child: const Text(
              'إلغاء',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              try {
                // تحديث حالة الدين إلى غير مدفوع
                final updatedDebt = debt.copyWith(
                  status: DebtStatus.pending,
                  paidAmount: 0.0,
                  updatedAt: DateTime.now(),
                );

                await DatabaseHelper().updateDebt(updatedDebt);

                if (context.mounted) {
                  // تحديث قائمة الديون
                  Provider.of<DebtProvider>(context, listen: false)
                      .loadMyDebts();

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم التراجع عن التسديد بنجاح'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.shade600,
              foregroundColor: Colors.white,
            ),
            child: const Text(
              'تأكيد التراجع',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDebtCard(Debt debt) {
    // استخراج اسم الدائن من الملاحظات
    String creditorName = 'دين عليه';
    if (debt.notes != null && debt.notes!.contains('دائن:')) {
      final lines = debt.notes!.split('\n');
      for (final line in lines) {
        if (line.startsWith('دائن:')) {
          creditorName = line.replaceFirst('دائن:', '').trim();
          break;
        }
      }
    }

    // تنسيق التاريخ مع اسم اليوم والوقت
    final List<String> arabicDays = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];

    final dayName = arabicDays[debt.entryDate.weekday - 1];
    final dateFormat = DateFormat('yyyy/MM/dd', 'en');

    String formatTimeInArabic(DateTime date) {
      final hour = date.hour;
      final minute = date.minute;

      if (hour == 0) {
        return '12:${minute.toString().padLeft(2, '0')} منتصف الليل';
      } else if (hour < 12) {
        return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} صباحاً';
      } else if (hour == 12) {
        return '12:${minute.toString().padLeft(2, '0')} ظهراً';
      } else {
        final hour12 = hour - 12;
        return '${hour12.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} مساءً';
      }
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // شريط الرأس الاحترافي
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.indigo.shade600,
                  Colors.indigo.shade700,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الدائن',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withValues(alpha: 0.8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        creditorName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: debt.isPaid
                        ? Colors.green.withValues(alpha: 0.2)
                        : Colors.orange.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: debt.isPaid
                          ? Colors.green.shade300
                          : Colors.orange.shade300,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        debt.isPaid ? Icons.check_circle : Icons.schedule,
                        size: 14,
                        color: debt.isPaid
                            ? Colors.green.shade200
                            : Colors.orange.shade200,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        debt.isPaid ? 'مدفوع' : 'معلق',
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                          color: debt.isPaid
                              ? Colors.green.shade200
                              : Colors.orange.shade200,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // محتوى البطاقة الاحترافي
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // بطاقات المعلومات المنفصلة
                Row(
                  children: [
                    // بطاقة المبلغ
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.grey.shade300,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade600,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Icon(
                                    Icons.attach_money,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'المبلغ',
                                  style: TextStyle(
                                    color: Colors.grey.shade700,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '${NumberFormat('#,###.000', 'en').format(debt.amount)} ألف',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            Text(
                              'المبلغ المستحق',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 10,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // بطاقة التاريخ
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.grey.shade300,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade600,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Icon(
                                    Icons.calendar_today,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'التاريخ',
                                  style: TextStyle(
                                    color: Colors.grey.shade700,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              dayName,
                              style: const TextStyle(
                                color: Colors.black,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '${dateFormat.format(debt.entryDate)} - ${formatTimeInArabic(debt.entryDate)}',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 10,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

                // إظهار تاريخ التسديد إذا كان مدفوع
                if (debt.isPaid && debt.firstPaymentDate != null) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.green.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check_circle,
                          color: Colors.green.shade600,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'تم التسديد',
                                style: TextStyle(
                                  color: Colors.green.shade700,
                                  fontSize: 13,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                '${dateFormat.format(debt.firstPaymentDate!)} - ${formatTimeInArabic(debt.firstPaymentDate!)}',
                                style: TextStyle(
                                  color: Colors.green.shade600,
                                  fontSize: 11,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.green.shade100,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Text(
                            _getTimeSincePayment(debt.firstPaymentDate!),
                            style: TextStyle(
                              color: Colors.green.shade800,
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                if (debt.notes != null && debt.notes!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'الملاحظات:',
                          style: TextStyle(
                            color: Colors.grey.shade700,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _getCleanNotes(debt.notes!),
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                const SizedBox(height: 20),

                // أزرار الإجراءات في صف واحد
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Column(
                    children: [
                      // جميع الأزرار في صف واحد متكيف
                      LayoutBuilder(
                        builder: (context, constraints) {
                          return Row(
                            children: [
                              // زر حالة الدفع
                              Expanded(
                                flex: debt.isPaid
                                    ? 1
                                    : 2, // تقليل الحجم عند ظهور زر الإلغاء
                                child: Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: debt.isPaid == true
                                          ? [
                                              Colors.green.shade600,
                                              Colors.green.shade700
                                            ]
                                          : [
                                              Colors.blue.shade600,
                                              Colors.blue.shade700
                                            ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(10),
                                    boxShadow: [
                                      BoxShadow(
                                        color: debt.isPaid == true
                                            ? Colors.green.shade300
                                            : Colors.blue.shade300,
                                        blurRadius: 6,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(10),
                                      onTap: () => _togglePaymentStatus(debt),
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: debt.isPaid ? 4 : 8,
                                          vertical: 12,
                                        ),
                                        child: FittedBox(
                                          fit: BoxFit.scaleDown,
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Icon(
                                                debt.isPaid == true
                                                    ? Icons.check_circle
                                                    : Icons.payment,
                                                size: debt.isPaid ? 14 : 16,
                                                color: Colors.white,
                                              ),
                                              SizedBox(
                                                  width: debt.isPaid ? 2 : 4),
                                              Text(
                                                debt.isPaid == true
                                                    ? 'مدفوع'
                                                    : 'دفع',
                                                style: const TextStyle(
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w600,
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),

                              const SizedBox(width: 4),

                              // زر التعديل
                              Expanded(
                                child: Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        Colors.blue.shade600,
                                        Colors.blue.shade700
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(10),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.blue.shade300,
                                        blurRadius: 6,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(10),
                                      onTap: () => _editDebt(debt),
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: debt.isPaid ? 2 : 4,
                                          vertical: 12,
                                        ),
                                        child: FittedBox(
                                          fit: BoxFit.scaleDown,
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Icon(
                                                Icons.edit,
                                                size: debt.isPaid ? 12 : 14,
                                                color: Colors.white,
                                              ),
                                              SizedBox(
                                                  width: debt.isPaid ? 1 : 2),
                                              const Text(
                                                'تعديل',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 11,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),

                              const SizedBox(width: 4),

                              // زر الحذف
                              Expanded(
                                child: Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        Colors.red.shade600,
                                        Colors.red.shade700
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(10),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.red.shade300,
                                        blurRadius: 6,
                                        offset: const Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(10),
                                      onTap: () => _deleteDebt(debt),
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: debt.isPaid ? 2 : 4,
                                          vertical: 12,
                                        ),
                                        child: FittedBox(
                                          fit: BoxFit.scaleDown,
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Icon(
                                                Icons.delete,
                                                size: debt.isPaid ? 12 : 14,
                                                color: Colors.white,
                                              ),
                                              SizedBox(
                                                  width: debt.isPaid ? 1 : 2),
                                              const Text(
                                                'حذف',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 11,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),

                              // زر التراجع عن التسديد (يظهر فقط للديون المدفوعة)
                              if (debt.isPaid) ...[
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          Colors.orange.shade600,
                                          Colors.orange.shade700
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      borderRadius: BorderRadius.circular(10),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.orange.shade300,
                                          blurRadius: 6,
                                          offset: const Offset(0, 3),
                                        ),
                                      ],
                                    ),
                                    child: Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        borderRadius: BorderRadius.circular(10),
                                        onTap: () => _undoPayment(debt),
                                        child: const Padding(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: 2,
                                            vertical: 12,
                                          ),
                                          child: FittedBox(
                                            fit: BoxFit.scaleDown,
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Icon(
                                                  Icons.undo,
                                                  size: 10,
                                                  color: Colors.white,
                                                ),
                                                SizedBox(width: 1),
                                                Text(
                                                  'إلغاء',
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 8,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showAddDebtDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const _AddMyDebtBottomSheet(),
    );
  }

  void _editDebt(Debt debt) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _EditMyDebtBottomSheet(debt: debt),
    );
  }

  void _deleteDebt(Debt debt) {
    // حذف مباشر بدون تأكيد
    _confirmDeleteDebt(debt);
  }

  Future<void> _confirmDeleteDebt(Debt debt) async {
    try {
      await DatabaseHelper().deleteDebt(debt.id!);

      if (mounted) {
        // تحديث قائمة الديون
        Provider.of<DebtProvider>(context, listen: false).loadMyDebts();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الدين بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء الحذف: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _togglePaymentStatus(Debt debt) {
    if (debt.isPaid == true) {
      // إذا كان مدفوع، قم بإلغاء الدفع مباشرة
      _markAsUnpaid(debt);
    } else {
      // إذا لم يكن مدفوع، قم بتسجيل الدفع مباشرة
      _showPaymentReceipt(debt);
    }
  }

  void _markAsUnpaid(Debt debt) async {
    try {
      // تحديث حالة الدين إلى غير مدفوع
      final updatedDebt = debt.copyWith(
        status: DebtStatus.pending,
        paidAmount: 0.0,
        updatedAt: DateTime.now(),
      );

      await DatabaseHelper().updateDebt(updatedDebt);

      if (context.mounted) {
        // تحديث قائمة الديون
        Provider.of<DebtProvider>(context, listen: false).loadMyDebts();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إلغاء الدفع'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showPaymentReceipt(Debt debt) {
    // استخراج اسم الدائن من الملاحظات
    String creditorName = 'دين عليه';
    if (debt.notes != null && debt.notes!.contains('دائن:')) {
      final lines = debt.notes!.split('\n');
      for (final line in lines) {
        if (line.startsWith('دائن:')) {
          creditorName = line.replaceFirst('دائن:', '').trim();
          break;
        }
      }
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        title: Row(
          children: [
            Icon(Icons.receipt, color: Colors.green.shade600, size: 24),
            const SizedBox(width: 8),
            Text(
              'إيصال الدفع',
              style: TextStyle(
                color: Colors.green.shade700,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // رأس الإيصال
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.green.shade600, Colors.green.shade700],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    const Icon(Icons.check_circle,
                        color: Colors.white, size: 48),
                    const SizedBox(height: 12),
                    const Text(
                      'تم الدفع بنجاح',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'إيصال رقم: ${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.green.shade100,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // تفاصيل الإيصال
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تفاصيل الدفع',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildReceiptRow('الدائن:', creditorName),
                    _buildReceiptRow('الصنف:', debt.cardType),
                    _buildReceiptRow('المبلغ:',
                        '${NumberFormat('#,###.000', 'en').format(debt.amount)} ألف'),
                    _buildReceiptRow(
                        'تاريخ الدفع:',
                        DateFormat('yyyy/MM/dd - HH:mm')
                            .format(DateTime.now())),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey.shade700,
            ),
            child: const Text(
              'إغلاق',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              try {
                // تحديث حالة الدين إلى مدفوع مع تاريخ التسديد
                final updatedDebt = debt.copyWith(
                  status: DebtStatus.paid,
                  paidAmount: debt.amount,
                  firstPaymentDate: DateTime.now(),
                  updatedAt: DateTime.now(),
                );

                await DatabaseHelper().updateDebt(updatedDebt);

                if (context.mounted) {
                  // تحديث قائمة الديون
                  Provider.of<DebtProvider>(context, listen: false)
                      .loadMyDebts();

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم تسجيل الدفع بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green.shade600,
              foregroundColor: Colors.white,
            ),
            child: const Text(
              'حفظ الإيصال',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReceiptRow(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      margin: const EdgeInsets.only(bottom: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.shade100),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 90,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.green.shade700,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
                fontSize: 15,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// نافذة إضافة دين مستقلة للديون التي عليه
class _AddMyDebtBottomSheet extends StatefulWidget {
  const _AddMyDebtBottomSheet();

  @override
  State<_AddMyDebtBottomSheet> createState() => _AddMyDebtBottomSheetState();
}

class _AddMyDebtBottomSheetState extends State<_AddMyDebtBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _creditorNameController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _selectedDate = DateTime.now();

  @override
  void dispose() {
    _creditorNameController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFF1A237E), Color(0xFF0D1B69)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(25),
                  topRight: Radius.circular(25),
                ),
              ),
              child: Row(
                children: [
                  const Expanded(
                    child: Text(
                      'إضافة دين عليه',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Form
            Expanded(
              child: Container(
                color: Colors.white,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // اسم الدائن
                        _buildTextField(
                          controller: _creditorNameController,
                          label: 'اسم الدائن',
                          icon: Icons.person,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال اسم الدائن';
                            }
                            return null;
                          },
                        ),

                        // المبلغ
                        _buildAmountTextField(),

                        // التاريخ مع اسم اليوم والوقت
                        _buildDateTimeField(),

                        // الملاحظات
                        _buildTextField(
                          controller: _notesController,
                          label: 'ملاحظات (اختياري)',
                          icon: Icons.note,
                          maxLines: 3,
                        ),

                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ),

            // Action Buttons
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade300,
                        foregroundColor: Colors.black87,
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'إلغاء',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _saveDebt,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF1A237E),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'حفظ',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // حقل المبلغ المحسّن مع فاصل الآلاف
  Widget _buildAmountTextField() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        controller: _amountController,
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
        ],
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return 'يرجى إدخال المبلغ';
          }
          final cleanValue = value.replaceAll(',', '');
          final amount = double.tryParse(cleanValue);
          if (amount == null || amount <= 0) {
            return 'يرجى إدخال مبلغ صحيح';
          }
          return null;
        },
        onChanged: (value) {
          // إزالة أي أحرف غير رقمية عدا النقطة والفاصلة
          String cleanValue = value.replaceAll(RegExp(r'[^0-9.]'), '');

          // منع أكثر من نقطة عشرية واحدة
          final dotCount = cleanValue.split('.').length - 1;
          if (dotCount > 1) {
            final lastDotIndex = cleanValue.lastIndexOf('.');
            cleanValue = cleanValue.substring(0, lastDotIndex) +
                cleanValue.substring(lastDotIndex + 1);
          }

          if (cleanValue.isNotEmpty && cleanValue != '.') {
            final number = double.tryParse(cleanValue);
            if (number != null) {
              // تنسيق الرقم مع فاصل الآلاف
              final formatter = NumberFormat('#,###.###', 'en');
              String formatted = formatter.format(number);

              // إزالة الأصفار الزائدة من النهاية
              if (formatted.contains('.')) {
                formatted = formatted.replaceAll(RegExp(r'\.?0+$'), '');
              }

              if (formatted != value) {
                final cursorPosition = formatted.length;
                _amountController.value = TextEditingValue(
                  text: formatted,
                  selection: TextSelection.collapsed(offset: cursorPosition),
                );
              }
            }
          }
        },
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black87,
          fontWeight: FontWeight.w600,
        ),
        decoration: InputDecoration(
          labelText: 'المبلغ (ألف)',
          labelStyle: const TextStyle(
            color: Color(0xFF424242),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: const Icon(Icons.attach_money,
              color: Color(0xFF424242), size: 22),
          suffixText: 'ألف',
          suffixStyle: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          filled: true,
          fillColor: Colors.grey.shade50,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFF1A237E), width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    int maxLines = 1,
    Function(String)? onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        controller: controller,
        validator: validator,
        keyboardType: keyboardType,
        maxLines: maxLines,
        onChanged: onChanged,
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black87,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: const TextStyle(
            color: Color(0xFF424242),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: Icon(icon, color: const Color(0xFF424242), size: 22),
          filled: true,
          fillColor: Colors.grey.shade50,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFF1A237E), width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
      ),
    );
  }

  Widget _buildDateTimeField() {
    // أسماء الأيام بالعربية
    final List<String> arabicDays = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];

    final dayName = arabicDays[_selectedDate.weekday - 1];
    final dateFormat = DateFormat('yyyy/MM/dd', 'en');

    // تحويل AM/PM إلى العربية
    String formatTimeInArabic(DateTime date) {
      final hour = date.hour;
      final minute = date.minute;

      if (hour == 0) {
        return '12:${minute.toString().padLeft(2, '0')} منتصف الليل';
      } else if (hour < 12) {
        return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} صباحاً';
      } else if (hour == 12) {
        return '12:${minute.toString().padLeft(2, '0')} ظهراً';
      } else {
        final hour12 = hour - 12;
        return '${hour12.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} مساءً';
      }
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: GestureDetector(
        onTap: _selectDateTime,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      color: Colors.red.shade600,
                      borderRadius: BorderRadius.circular(6),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.red.withValues(alpha: 0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: double.infinity,
                          height: 6,
                          decoration: BoxDecoration(
                            color: Colors.red.shade800,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(6),
                              topRight: Radius.circular(6),
                            ),
                          ),
                        ),
                        const Expanded(
                          child: Center(
                            child: Text(
                              '17',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'التاريخ والوقت',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF424242),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  Icon(Icons.arrow_drop_down,
                      color: Colors.grey.shade600, size: 24),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                '$dayName - ${dateFormat.format(_selectedDate)}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                formatTimeInArabic(_selectedDate),
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDateTime() async {
    // اختيار التاريخ
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar'),
    );

    if (pickedDate != null && mounted) {
      // اختيار الوقت
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_selectedDate),
      );

      if (pickedTime != null) {
        setState(() {
          _selectedDate = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            pickedTime.hour,
            pickedTime.minute,
          );
        });
      } else {
        setState(() {
          _selectedDate = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            _selectedDate.hour,
            _selectedDate.minute,
          );
        });
      }
    }
  }

  Future<void> _saveDebt() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      // تنظيف المبلغ من فواصل الآلاف
      final cleanAmount = _amountController.text.trim().replaceAll(',', '');
      final amount = double.tryParse(cleanAmount) ?? 0.0;

      // تحضير الملاحظات مع اسم الدائن
      String finalNotes = 'دائن: ${_creditorNameController.text.trim()}';
      if (_notesController.text.trim().isNotEmpty) {
        finalNotes += '\n${_notesController.text.trim()}';
      }

      // إنشاء أو الحصول على عميل وهمي للديون المستقلة
      final databaseHelper = DatabaseHelper();
      final int customerId = await _getOrCreateDummyCustomer(databaseHelper);

      final debt = Debt(
        customerId: customerId, // استخدام معرف العميل الوهمي
        itemName: _creditorNameController.text.trim(),
        cardType: _creditorNameController.text.trim(),
        quantity: 1, // كمية افتراضية
        amount: amount,
        notes: finalNotes,
        entryDate: _selectedDate,
        dueDate:
            _selectedDate.add(const Duration(days: 30)), // استحقاق بعد 30 يوم
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        direction: DebtDirection.iOweCustomer,
      );

      // حفظ في قاعدة البيانات
      await databaseHelper.insertDebt(debt);

      if (mounted) {
        // تحديث قائمة الديون
        Provider.of<DebtProvider>(context, listen: false).loadMyDebts();

        Navigator.of(context).pop();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الدين بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // دالة للحصول على أو إنشاء عميل وهمي للديون المستقلة
  Future<int> _getOrCreateDummyCustomer(DatabaseHelper databaseHelper) async {
    const dummyCustomerName = 'ديون مستقلة';
    const dummyCustomerPhone = '**********';

    // البحث عن العميل الوهمي الموجود
    final customers = await databaseHelper.getAllCustomers();
    final existingCustomer =
        customers.where((c) => c.name == dummyCustomerName).firstOrNull;

    if (existingCustomer != null) {
      return existingCustomer.id!;
    }

    // إنشاء عميل وهمي جديد
    final dummyCustomer = Customer(
      name: dummyCustomerName,
      phone: dummyCustomerPhone,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    return databaseHelper.insertCustomer(dummyCustomer);
  }
}

// نافذة تعديل دين مستقلة للديون التي عليه
class _EditMyDebtBottomSheet extends StatefulWidget {
  const _EditMyDebtBottomSheet({required this.debt});
  final Debt debt;

  @override
  State<_EditMyDebtBottomSheet> createState() => _EditMyDebtBottomSheetState();
}

class _EditMyDebtBottomSheetState extends State<_EditMyDebtBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _creditorNameController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  void _initializeFields() {
    // استخراج اسم الدائن من الملاحظات
    String creditorName = '';
    String additionalNotes = '';

    if (widget.debt.notes != null && widget.debt.notes!.contains('دائن:')) {
      final lines = widget.debt.notes!.split('\n');
      for (int i = 0; i < lines.length; i++) {
        final line = lines[i];
        if (line.startsWith('دائن:')) {
          creditorName = line.replaceFirst('دائن:', '').trim();
        } else if (line.trim().isNotEmpty) {
          if (additionalNotes.isNotEmpty) additionalNotes += '\n';
          additionalNotes += line;
        }
      }
    }

    _creditorNameController.text = creditorName;
    _amountController.text =
        NumberFormat('#,###.000', 'en').format(widget.debt.amount);
    _notesController.text = additionalNotes;
    _selectedDate = widget.debt.entryDate;
  }

  @override
  void dispose() {
    _creditorNameController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFF1A237E), Color(0xFF0D1B69)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(25),
                  topRight: Radius.circular(25),
                ),
              ),
              child: Row(
                children: [
                  const Expanded(
                    child: Text(
                      'تعديل الدين',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Form
            Expanded(
              child: Container(
                color: Colors.white,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // اسم الدائن
                        _buildTextField(
                          controller: _creditorNameController,
                          label: 'اسم الدائن',
                          icon: Icons.person,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال اسم الدائن';
                            }
                            return null;
                          },
                        ),

                        // المبلغ
                        _buildEditAmountTextField(),

                        // التاريخ مع اسم اليوم والوقت
                        _buildDateTimeField(),

                        // الملاحظات
                        _buildTextField(
                          controller: _notesController,
                          label: 'ملاحظات (اختياري)',
                          icon: Icons.note,
                          maxLines: 3,
                        ),

                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ),

            // Action Buttons
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade300,
                        foregroundColor: Colors.black87,
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'إلغاء',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _updateDebt,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF1A237E),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'حفظ التعديل',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // حقل المبلغ المحسّن مع فاصل الآلاف لنافذة التعديل
  Widget _buildEditAmountTextField() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        controller: _amountController,
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
        ],
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return 'يرجى إدخال المبلغ';
          }
          final cleanValue = value.replaceAll(',', '');
          final amount = double.tryParse(cleanValue);
          if (amount == null || amount <= 0) {
            return 'يرجى إدخال مبلغ صحيح';
          }
          return null;
        },
        onChanged: (value) {
          // إزالة أي أحرف غير رقمية عدا النقطة والفاصلة
          String cleanValue = value.replaceAll(RegExp(r'[^0-9.]'), '');

          // منع أكثر من نقطة عشرية واحدة
          final dotCount = cleanValue.split('.').length - 1;
          if (dotCount > 1) {
            final lastDotIndex = cleanValue.lastIndexOf('.');
            cleanValue = cleanValue.substring(0, lastDotIndex) +
                cleanValue.substring(lastDotIndex + 1);
          }

          if (cleanValue.isNotEmpty && cleanValue != '.') {
            final number = double.tryParse(cleanValue);
            if (number != null) {
              // تنسيق الرقم مع فاصل الآلاف
              final formatter = NumberFormat('#,###.###', 'en');
              String formatted = formatter.format(number);

              // إزالة الأصفار الزائدة من النهاية
              if (formatted.contains('.')) {
                formatted = formatted.replaceAll(RegExp(r'\.?0+$'), '');
              }

              if (formatted != value) {
                final cursorPosition = formatted.length;
                _amountController.value = TextEditingValue(
                  text: formatted,
                  selection: TextSelection.collapsed(offset: cursorPosition),
                );
              }
            }
          }
        },
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black87,
          fontWeight: FontWeight.w600,
        ),
        decoration: InputDecoration(
          labelText: 'المبلغ (ألف)',
          labelStyle: const TextStyle(
            color: Color(0xFF424242),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: const Icon(Icons.attach_money,
              color: Color(0xFF424242), size: 22),
          suffixText: 'ألف',
          suffixStyle: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          filled: true,
          fillColor: Colors.grey.shade50,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFF1A237E), width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    int maxLines = 1,
    Function(String)? onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: TextFormField(
        controller: controller,
        validator: validator,
        keyboardType: keyboardType,
        maxLines: maxLines,
        onChanged: onChanged,
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black87,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: const TextStyle(
            color: Color(0xFF424242),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: Icon(icon, color: const Color(0xFF424242), size: 22),
          filled: true,
          fillColor: Colors.grey.shade50,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFF1A237E), width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
      ),
    );
  }

  Widget _buildDateTimeField() {
    final List<String> arabicDays = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];

    final dayName = arabicDays[_selectedDate.weekday - 1];
    final dateFormat = DateFormat('yyyy/MM/dd', 'en');

    String formatTimeInArabic(DateTime date) {
      final hour = date.hour;
      final minute = date.minute;

      if (hour == 0) {
        return '12:${minute.toString().padLeft(2, '0')} منتصف الليل';
      } else if (hour < 12) {
        return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} صباحاً';
      } else if (hour == 12) {
        return '12:${minute.toString().padLeft(2, '0')} ظهراً';
      } else {
        final hour12 = hour - 12;
        return '${hour12.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} مساءً';
      }
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: GestureDetector(
        onTap: _selectDateTime,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      color: Colors.red.shade600,
                      borderRadius: BorderRadius.circular(6),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.red.withValues(alpha: 0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: double.infinity,
                          height: 6,
                          decoration: BoxDecoration(
                            color: Colors.red.shade800,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(6),
                              topRight: Radius.circular(6),
                            ),
                          ),
                        ),
                        const Expanded(
                          child: Center(
                            child: Text(
                              '17',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'التاريخ والوقت',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF424242),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  Icon(Icons.arrow_drop_down,
                      color: Colors.grey.shade600, size: 24),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                '$dayName - ${dateFormat.format(_selectedDate)}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                formatTimeInArabic(_selectedDate),
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDateTime() async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar'),
    );

    if (pickedDate != null && mounted) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_selectedDate),
      );

      if (pickedTime != null) {
        setState(() {
          _selectedDate = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            pickedTime.hour,
            pickedTime.minute,
          );
        });
      } else {
        setState(() {
          _selectedDate = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            _selectedDate.hour,
            _selectedDate.minute,
          );
        });
      }
    }
  }

  Future<void> _updateDebt() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      final cleanAmount = _amountController.text.trim().replaceAll(',', '');
      final amount = double.tryParse(cleanAmount) ?? 0.0;

      String finalNotes = 'دائن: ${_creditorNameController.text.trim()}';
      if (_notesController.text.trim().isNotEmpty) {
        finalNotes += '\n${_notesController.text.trim()}';
      }

      final updatedDebt = widget.debt.copyWith(
        itemName: _creditorNameController.text.trim(),
        cardType: _creditorNameController.text.trim(),
        amount: amount,
        notes: finalNotes,
        entryDate: _selectedDate,
        dueDate: _selectedDate.add(const Duration(days: 30)),
        updatedAt: DateTime.now(),
      );

      await DatabaseHelper().updateDebt(updatedDebt);

      if (mounted) {
        Provider.of<DebtProvider>(context, listen: false).loadMyDebts();
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث الدين بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
