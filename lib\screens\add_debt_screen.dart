import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../providers/debt_provider.dart';
import '../providers/card_type_provider.dart';
import '../providers/card_profit_provider.dart';
import '../providers/card_inventory_provider.dart';
import '../providers/notification_provider.dart';
import '../providers/customer_provider.dart';
import '../models/custom_card_type.dart';
import '../models/card_profit.dart';
import 'card_inventory_screen.dart';

class AddDebtScreen extends StatefulWidget {
  const AddDebtScreen({super.key, required this.customer, this.debt});

  final Customer customer;
  final Debt? debt;

  @override
  State<AddDebtScreen> createState() => _AddDebtScreenState();
}

class _AddDebtScreenState extends State<AddDebtScreen> {
  final _formKey = GlobalKey<FormState>();
  final _customerNameController = TextEditingController();
  final _quantityController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _entryDate = DateTime.now();
  DateTime _dueDate = DateTime.now().add(const Duration(days: 30));
  String? _selectedCardType =
      CardType.cash.name; // Store the ID of CardTypeOption
  bool _isLoading = false;

  bool get _isEditing => widget.debt != null;

  // Format amount with thousands separator
  String _formatAmount(double amount) {
    final formatter = NumberFormat('#,###', 'en');
    return formatter.format(amount);
  }

  // Parse amount from formatted string
  double _parseAmount(String text) {
    return double.tryParse(text.replaceAll(',', '')) ?? 0.0;
  }

  // حساب المبلغ تلقائياً بناءً على الكمية ونوع الكارت
  void _calculateAmount() {
    if (_selectedCardType == null || _quantityController.text.isEmpty) {
      return;
    }

    final quantity = int.tryParse(_quantityController.text) ?? 0;
    if (quantity <= 0) {
      return;
    }

    // الحصول على سعر الوحدة من المخزون
    final inventoryProvider = Provider.of<CardInventoryProvider>(
      context,
      listen: false,
    );
    final cardTypeProvider = Provider.of<CardTypeProvider>(
      context,
      listen: false,
    );

    try {
      // الحصول على اسم الكارت
      String cardTypeName = _selectedCardType!;

      // إذا كان نوع مخصص، احصل على الاسم الفعلي
      if (_selectedCardType!.startsWith('custom_')) {
        final cardTypeId =
            int.parse(_selectedCardType!.replaceFirst('custom_', ''));
        final customCardType = cardTypeProvider.customCardTypes.firstWhere(
          (ct) => ct.id == cardTypeId,
          orElse: () => throw Exception('Card type not found'),
        );
        cardTypeName = customCardType.displayName;
      } else {
        // للأنواع الافتراضية، احصل على الاسم المعروض
        final cardType = CardType.values.firstWhere(
          (ct) => ct.name == _selectedCardType,
          orElse: () => CardType.cash,
        );
        cardTypeName = cardType.displayName;
      }

      // الحصول على سعر الوحدة من المخزون
      final unitPrice = inventoryProvider.getCardPrice(cardTypeName);

      if (unitPrice > 0) {
        final totalAmount = unitPrice * quantity;
        final formattedAmount = _formatAmount(totalAmount);

        // تحديث حقل المبلغ فقط إذا لم يكن في وضع التعديل أو إذا كان المبلغ فارغاً
        if (!_isEditing || _amountController.text.isEmpty) {
          _amountController.text = formattedAmount;
        }
      }
    } catch (e) {
      debugPrint('خطأ في حساب المبلغ: $e');
    }
  }

  @override
  void initState() {
    super.initState();
    // Set customer name
    _customerNameController.text = widget.customer.name;

    if (_isEditing) {
      _quantityController.text = widget.debt!.quantity.toString();
      _amountController.text = _formatAmount(widget.debt!.amount);
      _notesController.text = widget.debt!.notes ?? '';
      _entryDate = widget.debt!.entryDate;
      _dueDate = widget.debt!.dueDate;
      // Initialize _selectedCardType in initState after fetching card types
      // We will set this after the CardTypeProvider is available
    }

    // إضافة listener لحقل الكمية لحساب المبلغ تلقائياً
    _quantityController.addListener(_calculateAmount);
    // It's better to load initial data that depends on providers after the first frame.
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final cardTypeProvider = Provider.of<CardTypeProvider>(
          context,
          listen: false,
        );

        // تحميل أنواع الكروت أولاً
        cardTypeProvider.loadCustomCardTypes().then((_) {
          if (mounted) {
            if (_isEditing) {
              final initialCardType = cardTypeProvider.getCardTypeById(
                widget.debt!.cardType,
              );
              setState(() {
                _selectedCardType = initialCardType?.id ?? CardType.cash.name;
              });
            } else {
              // تعيين قيمة افتراضية للديون الجديدة
              if (cardTypeProvider.allCardTypes.isNotEmpty) {
                final defaultCard = cardTypeProvider.allCardTypes.firstWhere(
                  (opt) => opt.id == CardType.cash.name,
                  orElse: () => cardTypeProvider.allCardTypes.first,
                );
                setState(() {
                  _selectedCardType = defaultCard.id;
                });
              }
            }
          }
        }).catchError((error) {
          debugPrint('Error loading card types: $error');
          // في حالة الخطأ، استخدم النوع الافتراضي
          if (mounted) {
            setState(() {
              _selectedCardType = CardType.cash.name;
            });
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _quantityController.removeListener(_calculateAmount);
    _customerNameController.dispose();
    _quantityController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _saveDebt() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // التحقق من توفر الكمية قبل البيع (للديون الجديدة فقط)
      if (!_isEditing) {
        final inventoryProvider = Provider.of<CardInventoryProvider>(
          context,
          listen: false,
        );
        final cardTypeProvider = Provider.of<CardTypeProvider>(
          context,
          listen: false,
        );

        final selectedCard = cardTypeProvider.allCardTypes.firstWhere(
          (card) => card.id == _selectedCardType,
        );
        final cardTypeName = selectedCard.displayName;
        final requestedQuantity = int.parse(_quantityController.text);
        final availableQuantity = inventoryProvider.getStockQuantity(
          cardTypeName,
        );

        if (availableQuantity < requestedQuantity) {
          setState(() {
            _isLoading = false;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'الكمية المطلوبة ($requestedQuantity) أكبر من المتوفر ($availableQuantity) من $cardTypeName',
                ),
                backgroundColor: Colors.red,
                action: SnackBarAction(
                  label: 'إدارة الكميات',
                  textColor: Colors.white,
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CardInventoryScreen(),
                      ),
                    );
                  },
                ),
              ),
            );
          }
          return;
        }
      }

      final now = DateTime.now();

      // Create debt object

      final debt = Debt(
        id: _isEditing ? widget.debt!.id : null,
        customerId: widget.customer.id!,
        itemName: _selectedCardType!, // استخدام نوع الكارت كاسم الصنف
        quantity: int.parse(_quantityController.text),
        amount: _parseAmount(_amountController.text),
        paidAmount: _isEditing ? widget.debt!.paidAmount : 0.0,
        cardType: _selectedCardType!,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        entryDate: _entryDate,
        dueDate: _dueDate,
        createdAt: _isEditing ? widget.debt!.createdAt : now,
        updatedAt: now,
        status: _isEditing ? widget.debt!.status : DebtStatus.pending,
      );

      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      final cardTypeProvider = Provider.of<CardTypeProvider>(
        context,
        listen: false,
      );
      final profitProvider = Provider.of<CardProfitProvider>(
        context,
        listen: false,
      );
      final inventoryProvider = Provider.of<CardInventoryProvider>(
        context,
        listen: false,
      );

      if (_isEditing) {
        await debtProvider.updateDebt(debt);

        // تحديث اسم العميل إذا تم تغييره
        if (_customerNameController.text.trim() != widget.customer.name &&
            mounted) {
          final customerProvider = Provider.of<CustomerProvider>(
            context,
            listen: false,
          );
          final updatedCustomer = Customer(
            id: widget.customer.id,
            name: _customerNameController.text.trim(),
            phone: widget.customer.phone,
            creditLimit: widget.customer.creditLimit,
            limitNotes: widget.customer.limitNotes,
            createdAt: widget.customer.createdAt,
            updatedAt: DateTime.now(),
          );
          await customerProvider.updateCustomer(updatedCustomer);
        }
      } else {
        await debtProvider.addDebt(debt);

        // تسجيل الربح التلقائي عند إضافة دين جديد (البيع)
        await _recordProfitAutomatically(
          profitProvider,
          debt.cardType,
          debt.amount,
          debt.quantity,
        );

        // استقطاع الكمية التلقائي من المخزون
        await _deductFromInventoryAutomatically(
          inventoryProvider,
          debt.cardType,
          debt.quantity,
        );

        // Refresh debts if we're viewing this customer's debts
        if (debtProvider.currentCustomerId == widget.customer.id) {
          await debtProvider.refreshCurrentCustomerDebts();
        }
      }

      // تحديث البيانات فوراً
      await Future.wait([
        cardTypeProvider.loadCustomCardTypes(),
        profitProvider.loadProfits(), // تحديث الأرباح
        inventoryProvider.loadInventories(), // تحديث المخزون
        debtProvider.refreshCurrentCustomerDebts(),
      ]);

      debugPrint('🔄 تم تحديث جميع البيانات بنجاح');

      if (mounted) {
        Navigator.pop(context, true); // إرجاع true للإشارة إلى أن التحديث تم

        // عرض رسالة تنبيه البيع للديون الجديدة فقط
        if (!_isEditing) {
          _showSaleNotification(debt);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث الدين بنجاح'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // عرض رسالة تنبيه البيع في أعلى الشاشة
  void _showSaleNotification(Debt debt) {
    // التحقق من إعدادات التنبيهات أولاً
    final notificationProvider = Provider.of<NotificationProvider>(
      context,
      listen: false,
    );

    // إذا كانت تنبيهات البيع معطلة، لا تعرض شيء
    if (!notificationProvider.settings.salesNotificationsEnabled) {
      return;
    }

    final cardInventoryProvider = Provider.of<CardInventoryProvider>(
      context,
      listen: false,
    );
    final cardTypeProvider = Provider.of<CardTypeProvider>(
      context,
      listen: false,
    );

    // الحصول على اسم الكارت الفعلي
    String cardTypeName = debt.cardType;

    // إذا كان نوع مخصص، احصل على الاسم الفعلي
    if (debt.cardType.startsWith('custom_')) {
      final cardTypeId = int.parse(debt.cardType.replaceFirst('custom_', ''));
      final customCardType = cardTypeProvider.customCardTypes.firstWhere(
        (ct) => ct.id == cardTypeId,
        orElse: () => throw Exception('Card type not found'),
      );
      cardTypeName = customCardType.displayName;
    } else {
      // للأنواع الافتراضية، احصل على الاسم المعروض
      final cardType = CardType.values.firstWhere(
        (ct) => ct.name == debt.cardType,
        orElse: () => CardType.cash,
      );
      cardTypeName = cardType.displayName;
    }

    final remainingStock = cardInventoryProvider.getStockQuantity(cardTypeName);

    final notificationMessage = '🎉 تم بيع ${debt.quantity} $cardTypeName\n'
        '📦 متبقي في المخزون: $remainingStock';

    // عرض التنبيه في أعلى الشاشة باستخدام Overlay
    _showTopNotification(notificationMessage);
  }

  // عرض رسالة تنبيه في أعلى الشاشة
  void _showTopNotification(String message) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 10, // أسفل شريط الحالة مباشرة
        left: 16,
        right: 16,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade600, Colors.green.shade700],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    message,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                      height: 1.3,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () => overlayEntry.remove(),
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // إزالة الرسالة تلقائياً حسب إعدادات التنبيهات
    final notificationProvider = Provider.of<NotificationProvider>(
      context,
      listen: false,
    );
    final duration = notificationProvider.settings.notificationDuration;

    Timer(Duration(seconds: duration), () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  // تسجيل الربح التلقائي عند البيع
  Future<void> _recordProfitAutomatically(
    CardProfitProvider profitProvider,
    String cardType,
    double amount,
    int quantity,
  ) async {
    try {
      // أسعار الشراء الافتراضية لكل نوع بطاقة
      final Map<String, double> defaultCostPrices = {
        'زين': 6500.0,
        'آسيا': 7500.0,
        'أبو العشرة': 9800.0,
        'أبو الستة': 4800.0,
        'نقدي': 0.0, // لا يوجد تكلفة للنقد
      };

      // حساب سعر البيع للوحدة الواحدة
      final sellingPricePerUnit = amount / quantity;

      // الحصول على سعر الشراء الافتراضي
      final costPrice = defaultCostPrices[cardType] ??
          sellingPricePerUnit * 0.9; // افتراض ربح 10%

      // تسجيل الربح فقط إذا كان هناك ربح فعلي
      if (sellingPricePerUnit > costPrice) {
        final profit = CardProfit(
          cardType: cardType,
          costPrice: costPrice,
          sellingPrice: sellingPricePerUnit,
          quantity: quantity,
          date: DateTime.now(),
        );

        await profitProvider.addProfit(profit);
        debugPrint('✅ تم تسجيل ربح تلقائي: ${profit.totalProfit} دينار');
      }
    } catch (e) {
      debugPrint('خطأ في تسجيل الربح التلقائي: $e');
    }
  }

  // استقطاع الكمية التلقائي من المخزون
  Future<void> _deductFromInventoryAutomatically(
    CardInventoryProvider inventoryProvider,
    String cardType,
    int quantity,
  ) async {
    try {
      // محاولة استقطاع الكمية
      final success = await inventoryProvider.deductStock(cardType, quantity);

      if (success) {
        debugPrint('✅ تم استقطاع $quantity من $cardType من المخزون');
      } else {
        debugPrint(
          '⚠️ فشل في استقطاع $quantity من $cardType - قد تكون الكمية غير كافية',
        );

        // إظهار تنبيه للمستخدم
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تنبيه: الكمية المتوفرة من $cardType قد لا تكون كافية',
              ),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في استقطاع المخزون: $e');
    }
  }

  Future<void> _selectDate(BuildContext context, bool isEntryDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isEntryDate ? _entryDate : _dueDate,
      firstDate: DateTime(2020), // السماح باختيار تواريخ سابقة لكلا التاريخين
      lastDate: DateTime(2030),
      locale: const Locale('ar'),
      helpText: isEntryDate ? 'اختيار تاريخ القيد' : 'اختيار تاريخ الاستحقاق',
      cancelText: 'إلغاء',
      confirmText: 'تأكيد',
    );

    if (picked != null) {
      setState(() {
        if (isEntryDate) {
          // لتاريخ القيد، احتفظ بالوقت الحالي
          final currentTime = TimeOfDay.fromDateTime(_entryDate);
          _entryDate = DateTime(
            picked.year,
            picked.month,
            picked.day,
            currentTime.hour,
            currentTime.minute,
          );
        } else {
          // لتاريخ الاستحقاق، نضع الوقت في نهاية اليوم
          _dueDate = DateTime(picked.year, picked.month, picked.day, 23, 59);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('yyyy/MM/dd');

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isEditing ? 'تعديل الدين' : 'إضافة دين جديد',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue.shade800, Colors.blue.shade900],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveDebt,
              child: const Text(
                'حفظ',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Customer Info
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(Icons.person, color: Theme.of(context).primaryColor),
                  const SizedBox(width: 8),
                  Text(
                    'العميل: ${widget.customer.name}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Customer Name Field
            _buildTextField(
              controller: _customerNameController,
              label: 'اسم العميل',
              icon: Icons.person_outline,
              enabled: _isEditing, // يمكن تعديل اسم العميل في وضع التعديل فقط
              validator: _isEditing
                  ? (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال اسم العميل';
                      }
                      return null;
                    }
                  : null,
            ),

            const SizedBox(height: 16),

            // Quantity and Amount
            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: _quantityController,
                    label: 'الكمية',
                    icon: Icons.numbers,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال الكمية';
                      }
                      if (int.tryParse(value) == null ||
                          int.parse(value) <= 0) {
                        return 'يرجى إدخال كمية صحيحة';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildTextField(
                    controller: _amountController,
                    label: 'المبلغ',
                    icon: Icons.attach_money,
                    keyboardType: TextInputType.number,
                    inputFormatters: [ThousandsFormatter()],
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال المبلغ';
                      }
                      final amount = _parseAmount(value);
                      if (amount <= 0) {
                        return 'يرجى إدخال مبلغ صحيح';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Card Type
            Consumer<CardTypeProvider>(
              builder: (context, provider, child) {
                // إضافة تحميل البيانات إذا لم تكن محملة
                if (provider.allCardTypes.isEmpty && !provider.isLoading) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    provider.loadCustomCardTypes();
                  });
                }

                if (provider.isLoading) {
                  return Container(
                    height: 60,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(12),
                      color: Colors.grey[50],
                    ),
                    child: const Center(
                      child: Row(
                        children: [
                          SizedBox(width: 16),
                          Icon(Icons.credit_card, color: Colors.grey),
                          SizedBox(width: 16),
                          Text(
                            'جاري تحميل أنواع الكروت...',
                            style: TextStyle(color: Colors.grey),
                          ),
                          Spacer(),
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 16),
                        ],
                      ),
                    ),
                  );
                }

                if (provider.allCardTypes.isEmpty) {
                  // إنشاء قائمة افتراضية إذا لم تكن البيانات محملة
                  final defaultCardTypes = [
                    DropdownMenuItem<String>(
                      value: CardType.cash.name,
                      child: Text(
                        CardType.cash.displayName,
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    DropdownMenuItem<String>(
                      value: CardType.zain.name,
                      child: Text(
                        CardType.zain.displayName,
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    DropdownMenuItem<String>(
                      value: CardType.sia.name,
                      child: Text(
                        CardType.sia.displayName,
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    DropdownMenuItem<String>(
                      value: CardType.abuAshara.name,
                      child: Text(
                        CardType.abuAshara.displayName,
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    DropdownMenuItem<String>(
                      value: CardType.abuSitta.name,
                      child: Text(
                        CardType.abuSitta.displayName,
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ];

                  // تعيين قيمة افتراضية إذا لم تكن محددة
                  if (_selectedCardType == null) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      setState(() {
                        _selectedCardType = CardType.cash.name;
                      });
                    });
                  }

                  return DropdownButtonFormField<String>(
                    value: _selectedCardType ?? CardType.cash.name,
                    style: const TextStyle(color: Colors.black87, fontSize: 16),
                    decoration: InputDecoration(
                      labelText: 'نوع الكارت',
                      labelStyle: const TextStyle(
                        color: Colors.black54,
                        fontSize: 14,
                      ),
                      prefixIcon: const Icon(
                        Icons.credit_card,
                        color: Colors.black54,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.grey),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.grey),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(
                          color: Colors.blue,
                          width: 2,
                        ),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 16,
                      ),
                    ),
                    isExpanded: true,
                    items: defaultCardTypes,
                    onChanged: (value) {
                      setState(() {
                        _selectedCardType = value;
                      });
                      // حساب المبلغ عند تغيير نوع الكارت
                      _calculateAmount();
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى اختيار نوع الكارت';
                      }
                      return null;
                    },
                  );
                }

                // Ensure _selectedCardType is valid or set a default
                if (_selectedCardType == null ||
                    !provider.allCardTypes.any(
                      (opt) => opt.id == _selectedCardType,
                    )) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    setState(() {
                      _selectedCardType = provider.allCardTypes.first.id;
                    });
                  });
                }

                return DropdownButtonFormField<String>(
                  value: _selectedCardType,
                  style: const TextStyle(color: Colors.black87, fontSize: 16),
                  decoration: InputDecoration(
                    labelText: 'نوع الكارت',
                    labelStyle: const TextStyle(
                      color: Colors.black54,
                      fontSize: 14,
                    ),
                    prefixIcon: const Icon(
                      Icons.credit_card,
                      color: Colors.black54,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.grey),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.grey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Colors.blue,
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                  ),
                  isExpanded: true, // Fix overflow issue
                  items: provider.allCardTypes.toSet().map((option) {
                    return DropdownMenuItem<String>(
                      value: option.id,
                      child: Text(
                        option.displayName,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black87,
                        ),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCardType = value;
                    });
                    // حساب المبلغ عند تغيير نوع الكارت
                    _calculateAmount();
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى اختيار نوع الكارت';
                    }
                    return null;
                  },
                );
              },
            ),

            const SizedBox(height: 16),

            // Dates
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(context, true),
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.white,
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            color: Colors.black54,
                          ),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'تاريخ القيد',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.black54,
                                ),
                              ),
                              Text(
                                dateFormat.format(_entryDate),
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(context, false),
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.white,
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.schedule, color: Colors.black54),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'تاريخ الاستحقاق',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.black54,
                                ),
                              ),
                              Text(
                                dateFormat.format(_dueDate),
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Notes
            _buildTextField(
              controller: _notesController,
              label: 'ملاحظات (اختياري)',
              icon: Icons.note,
              maxLines: 3,
            ),

            const SizedBox(height: 32),

            // Save Button
            ElevatedButton(
              onPressed: _isLoading ? null : _saveDebt,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      _isEditing ? 'تحديث الدين' : 'إضافة الدين',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    int maxLines = 1,
    bool enabled = true,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      maxLines: maxLines,
      enabled: enabled,
      inputFormatters: inputFormatters,
      style: const TextStyle(color: Colors.black87, fontSize: 16),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: const TextStyle(color: Colors.black54, fontSize: 14),
        prefixIcon: Icon(icon, color: Colors.black54),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.grey),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.grey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.blue, width: 2),
        ),
        filled: true,
        fillColor: enabled ? Colors.white : Colors.grey[100],
      ),
      validator: validator,
    );
  }
}

// Custom formatter for amount with thousands separator
class ThousandsFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // Remove all non-digit characters
    final String digitsOnly = newValue.text.replaceAll(RegExp(r'[^0-9]'), '');

    if (digitsOnly.isEmpty) {
      return const TextEditingValue();
    }

    // Format with thousands separator
    final formatter = NumberFormat('#,###', 'en');
    final String formatted = formatter.format(int.parse(digitsOnly));

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
