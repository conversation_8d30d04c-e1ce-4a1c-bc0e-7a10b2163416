import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/debt.dart';
import '../models/customer.dart';
import '../widgets/debt_card.dart';

import '../providers/card_type_provider.dart';
import '../providers/debt_provider.dart';
import '../database/database_helper.dart';
import '../utils/number_formatter.dart';

// أنواع عرض البطاقات
enum DailySalesViewType {
  standard('عادي'),
  compact('مضغوط');

  const DailySalesViewType(this.label);
  final String label;
}

class DailySalesDebtsScreen extends StatefulWidget {
  const DailySalesDebtsScreen({
    super.key,
    required this.isToday,
    required this.title,
  });

  final bool isToday;
  final String title;

  @override
  State<DailySalesDebtsScreen> createState() => _DailySalesDebtsScreenState();
}

class _DailySalesDebtsScreenState extends State<DailySalesDebtsScreen>
    with TickerProviderStateMixin {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Debt> _debts = [];
  Map<int, Customer> _customers = {};
  bool _isLoading = true;
  DailySalesViewType _currentViewType = DailySalesViewType.standard;
  bool _isStatisticsExpanded = false;
  late AnimationController _statisticsAnimationController;
  late Animation<double> _statisticsAnimation;

  @override
  void initState() {
    super.initState();
    _statisticsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _statisticsAnimation = CurvedAnimation(
      parent: _statisticsAnimationController,
      curve: Curves.easeInOut,
    );
    _loadDebts();
  }

  @override
  void dispose() {
    _statisticsAnimationController.dispose();
    super.dispose();
  }

  Future<void> _loadDebts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // جلب جميع الديون
      final allDebts = await _databaseHelper.getAllDebts();
      final now = DateTime.now();
      final targetDate = widget.isToday
          ? DateTime(now.year, now.month, now.day)
          : DateTime(
              now.year,
              now.month,
              now.day,
            ).subtract(const Duration(days: 1));

      // فلترة الديون حسب التاريخ
      final filteredDebts = allDebts.where((debt) {
        final debtDay = DateTime(
          debt.entryDate.year,
          debt.entryDate.month,
          debt.entryDate.day,
        );
        return debtDay.isAtSameMomentAs(targetDate);
      }).toList();

      // جلب معلومات العملاء
      final customerIds = filteredDebts.map((debt) => debt.customerId).toSet();
      final customers = <int, Customer>{};

      for (final customerId in customerIds) {
        final customer = await _databaseHelper.getCustomer(customerId);
        if (customer != null) {
          customers[customerId] = customer;
        }
      }

      setState(() {
        _debts = filteredDebts;
        _customers = customers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: const Color(0xFF00695C),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          // زر الإحصائيات
          IconButton(
            onPressed: _toggleStatistics,
            icon: AnimatedRotation(
              turns: _isStatisticsExpanded ? 0.5 : 0,
              duration: const Duration(milliseconds: 300),
              child: Icon(
                _isStatisticsExpanded
                    ? Icons.expand_less
                    : Icons.analytics_outlined,
                color: Colors.white,
              ),
            ),
            tooltip:
                _isStatisticsExpanded ? 'إخفاء الإحصائيات' : 'إظهار الإحصائيات',
          ),

          // زر نوع العرض
          PopupMenuButton<DailySalesViewType>(
            icon: Icon(
              _currentViewType == DailySalesViewType.compact
                  ? Icons.view_compact
                  : Icons.view_comfortable,
              size: 20,
              color: Colors.white,
            ),
            tooltip: 'نوع العرض',
            onSelected: (viewType) {
              setState(() {
                _currentViewType = viewType;
              });
            },
            itemBuilder: (context) => DailySalesViewType.values.map((viewType) {
              return PopupMenuItem<DailySalesViewType>(
                value: viewType,
                child: Row(
                  children: [
                    Icon(
                      viewType == DailySalesViewType.compact
                          ? Icons.view_compact
                          : Icons.view_comfortable,
                      size: 18,
                      color: _currentViewType == viewType
                          ? Colors.green.shade600
                          : Colors.grey.shade600,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      viewType.label,
                      style: TextStyle(
                        color: _currentViewType == viewType
                            ? Colors.green.shade600
                            : Colors.black87,
                        fontWeight: _currentViewType == viewType
                            ? FontWeight.w600
                            : FontWeight.normal,
                      ),
                    ),
                    if (_currentViewType == viewType) ...[
                      const Spacer(),
                      Icon(
                        Icons.check,
                        size: 16,
                        color: Colors.green.shade600,
                      ),
                    ],
                  ],
                ),
              );
            }).toList(),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          // قسم الإحصائيات القابل للطي
          SizeTransition(
            sizeFactor: _statisticsAnimation,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: const Color(0xFF00695C).withValues(alpha: 0.05),
                border: Border(
                  bottom: BorderSide(
                    color: const Color(0xFF00695C).withValues(alpha: 0.2),
                  ),
                ),
              ),
              child: _buildStatisticsContent(),
            ),
          ),

          // المحتوى الرئيسي
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _debts.isEmpty
                    ? _buildEmptyState()
                    : _currentViewType == DailySalesViewType.compact
                        ? _buildCompactView()
                        : _buildStandardView(),
          ),
        ],
      ),
    );
  }

  // تبديل حالة الإحصائيات
  void _toggleStatistics() {
    setState(() {
      _isStatisticsExpanded = !_isStatisticsExpanded;
      if (_isStatisticsExpanded) {
        _statisticsAnimationController.forward();
      } else {
        _statisticsAnimationController.reverse();
      }
    });
  }

  // بناء محتوى الإحصائيات
  Widget _buildStatisticsContent() {
    if (_debts.isEmpty) {
      return const SizedBox.shrink();
    }

    // حساب الإحصائيات
    final stats = _calculateStatistics();

    return SizedBox(
      height: 400, // ارتفاع ثابت للمنطقة القابلة للتمرير
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        physics: const BouncingScrollPhysics(),
        child: Column(
          children: [
            // بطاقة إجمالي المبلغ العريضة
            _buildWideAmountCard(
              title: 'إجمالي المبلغ',
              value: '${(stats['totalAmount'] / 1000).toStringAsFixed(3)} ألف',
              icon: Icons.attach_money,
              color: const Color(0xFF00695C),
            ),

            const SizedBox(height: 20),

            // بطاقة إجمالي الكروت العريضة
            _buildWideAmountCard(
              title: 'إجمالي الكروت',
              value: '${stats['totalCards']}',
              icon: Icons.credit_card,
              color: const Color(0xFF00695C),
            ),

            const SizedBox(height: 20),

            // أنواع الكروت مع المخطط
            _buildCardTypesSection(stats),

            const SizedBox(height: 20),

            // نسبة البيع
            _buildSalesRatioCard(stats),

            const SizedBox(height: 20), // مساحة إضافية في النهاية
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.receipt_long_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            widget.isToday ? 'لا توجد ديون اليوم' : 'لا توجد ديون الأمس',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم إضافة أي ديون في هذا التاريخ',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  // العرض العادي
  Widget _buildStandardView() {
    return RefreshIndicator(
      onRefresh: _loadDebts,
      child: Consumer<DebtProvider>(
        builder: (context, debtProvider, child) {
          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _debts.length,
            itemBuilder: (context, index) {
              final debt = _debts[index];
              final customer = _customers[debt.customerId];

              if (customer == null) {
                return const SizedBox.shrink();
              }

              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: DebtCard(
                  debt: debt,
                  disableLongPress:
                      true, // تعطيل الضغط المطول في شاشة بيع اليوم
                  onDelete: () {
                    // إعادة تحميل البيانات عند الحذف
                    _loadDebts();
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }

  // العرض المضغوط
  Widget _buildCompactView() {
    return RefreshIndicator(
      onRefresh: _loadDebts,
      child: Consumer<DebtProvider>(
        builder: (context, debtProvider, child) {
          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _debts.length,
            itemBuilder: (context, index) {
              final debt = _debts[index];
              final customer = _customers[debt.customerId];

              if (customer == null) {
                return const SizedBox.shrink();
              }

              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: _buildCompactDebtCard(debt, customer),
              );
            },
          );
        },
      ),
    );
  }

  // بناء بطاقة دين مضغوطة (نفس تصميم نظرة عامة للديون)
  Widget _buildCompactDebtCard(Debt debt, Customer customer) {
    // التحقق من انتهاء الموعد وتاريخ أمس واليوم
    final isOverdue = debt.dueDate.isBefore(DateTime.now());
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final debtDate = DateTime(
      debt.entryDate.year,
      debt.entryDate.month,
      debt.entryDate.day,
    );
    final isToday = debtDate.isAtSameMomentAs(today);
    final isYesterday = debtDate.isAtSameMomentAs(yesterday);

    // تحديد لون البطاقة
    Color cardColor;
    Color borderColor;
    Color shadowColor;

    if (isOverdue) {
      cardColor = Colors.red.shade600;
      borderColor = Colors.red.shade700;
      shadowColor = Colors.red.withValues(alpha: 0.3);
    } else if (isYesterday) {
      cardColor = const Color(0xFF1A237E); // أزرق داكن مائل للأسود
      borderColor = const Color(0xFF0D1B69); // أزرق أكثر قتامة للحدود
      shadowColor = const Color(0xFF1A237E).withValues(alpha: 0.4);
    } else if (isToday) {
      cardColor = Colors.white;
      borderColor = Colors.green.shade300; // أخضر خفيف لبطاقات اليوم
      shadowColor = Colors.green.withValues(alpha: 0.1);
    } else {
      cardColor = Colors.white;
      borderColor = Colors.grey.shade200;
      shadowColor = Colors.grey.withValues(alpha: 0.05);
    }

    final isColored = isOverdue || isYesterday;

    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: borderColor,
        ),
        boxShadow: [
          BoxShadow(
            color: shadowColor,
            blurRadius: isColored ? 4 : 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الصف الأول: اسم العميل والمبلغ
          Row(
            children: [
              // اسم العميل
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      Icons.person,
                      color: isColored ? Colors.white : Colors.blue.shade600,
                      size: 14,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'العميل:',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: isColored
                            ? Colors.white.withValues(alpha: 0.8)
                            : Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        customer.name,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: isColored ? Colors.white : Colors.black87,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 12),

              // المبلغ
              Row(
                children: [
                  Icon(
                    Icons.attach_money,
                    color: isColored ? Colors.white : Colors.green.shade600,
                    size: 14,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'المبلغ:',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: isColored
                          ? Colors.white.withValues(alpha: 0.8)
                          : Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${(debt.remainingAmount / 1000).toStringAsFixed(3)} ألف',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: isColored ? Colors.white : Colors.green.shade700,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 6),

          const SizedBox(height: 6),

          // الصف الثاني: اسم الكارت والكمية
          Row(
            children: [
              // اسم الكارت
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      Icons.credit_card,
                      color: isColored ? Colors.white : Colors.orange.shade600,
                      size: 14,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'اسم الكارت:',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: isColored
                            ? Colors.white.withValues(alpha: 0.8)
                            : Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        _convertCardTypeToArabic(debt.cardType),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color:
                              isColored ? Colors.white : Colors.orange.shade700,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 12),

              // الكمية
              Row(
                children: [
                  Icon(
                    Icons.inventory,
                    color: isColored ? Colors.white : Colors.purple.shade600,
                    size: 14,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'الكمية:',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: isColored
                          ? Colors.white.withValues(alpha: 0.8)
                          : Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${debt.quantity}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: isColored ? Colors.white : Colors.purple.shade700,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 6),

          // الصف الثالث: تاريخ القيد والوقت
          Row(
            children: [
              // تاريخ القيد
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      color: isColored ? Colors.white : Colors.blue.shade600,
                      size: 14,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'تاريخ القيد:',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: isColored
                            ? Colors.white.withValues(alpha: 0.8)
                            : Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        _formatFullDateWithDay(debt.entryDate),
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                          color:
                              isColored ? Colors.white : Colors.blue.shade700,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 12),

              // الوقت
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    color: isColored ? Colors.white : Colors.indigo.shade600,
                    size: 14,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'الوقت:',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: isColored
                          ? Colors.white.withValues(alpha: 0.8)
                          : Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatTimeOnly(debt.entryDate),
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      color: isColored ? Colors.white : Colors.indigo.shade700,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 6),

          // الصف الرابع: تاريخ الاستحقاق
          Row(
            children: [
              Icon(
                Icons.event,
                color: isColored ? Colors.white : Colors.red.shade600,
                size: 14,
              ),
              const SizedBox(width: 6),
              Text(
                'تاريخ الاستحقاق:',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: isColored
                      ? Colors.white.withValues(alpha: 0.8)
                      : Colors.grey.shade600,
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  _formatFullDateWithDay(debt.dueDate),
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: isColored ? Colors.white : Colors.red.shade700,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          const SizedBox(height: 6),

          // الصف الخامس: العدادات
          Row(
            children: [
              // عداد منذ القيد
              Expanded(
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isColored
                        ? Colors.white.withValues(alpha: 0.2)
                        : Colors.white,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: isColored
                          ? Colors.white.withValues(alpha: 0.3)
                          : Colors.grey.shade300,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        color: isColored ? Colors.white : Colors.grey.shade600,
                        size: 12,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          'منذ القيد: ${_getTimeSinceEntry(debt.entryDate)}',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color:
                                isColored ? Colors.white : Colors.grey.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // عداد المتبقي للموعد
              Expanded(
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isColored
                        ? Colors.white.withValues(alpha: 0.2)
                        : Colors.white,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: isColored
                          ? Colors.white.withValues(alpha: 0.3)
                          : Colors.grey.shade300,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _getTimeUntilDue(debt.dueDate).contains('متأخر')
                            ? Icons.warning
                            : Icons.timer,
                        color: isColored
                            ? Colors.white
                            : (_getTimeUntilDue(debt.dueDate).contains('متأخر')
                                ? Colors.red.shade600
                                : Colors.grey.shade600),
                        size: 12,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          'للموعد: ${_getTimeUntilDue(debt.dueDate)}',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color: isColored
                                ? Colors.white
                                : (_getTimeUntilDue(debt.dueDate)
                                        .contains('متأخر')
                                    ? Colors.red.shade700
                                    : Colors.grey.shade700),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          // التفاصيل (إذا وجدت)
          if (debt.notes?.isNotEmpty == true) ...[
            const SizedBox(height: 6),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.notes,
                  color: isColored ? Colors.white : Colors.indigo.shade600,
                  size: 14,
                ),
                const SizedBox(width: 6),
                Text(
                  'التفاصيل:',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: isColored
                        ? Colors.white.withValues(alpha: 0.8)
                        : Colors.grey.shade600,
                  ),
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    debt.notes!,
                    style: TextStyle(
                      fontSize: 11,
                      color: isColored
                          ? Colors.white.withValues(alpha: 0.9)
                          : Colors.indigo.shade700,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  // حساب الإحصائيات
  Map<String, dynamic> _calculateStatistics() {
    double totalAmount = 0;
    int totalCards = 0;
    final Map<String, int> cardTypes = {};

    for (final debt in _debts) {
      totalAmount += debt.remainingAmount;
      totalCards += debt.quantity;

      final cardType = _convertCardTypeToArabic(debt.cardType);
      cardTypes[cardType] = (cardTypes[cardType] ?? 0) + debt.quantity;
    }

    return {
      'totalAmount': totalAmount,
      'totalCards': totalCards,
      'cardTypes': cardTypes,
    };
  }

  // بناء بطاقة المبلغ العريضة
  Widget _buildWideAmountCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 2),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.15),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        children: [
          // العنوان
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
              letterSpacing: 0.5,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),

          // الأيقونة والقيمة في صف واحد
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // الأيقونة
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: color.withValues(alpha: 0.3)),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 32,
                ),
              ),
              const SizedBox(width: 24),

              // القيمة في الوسط
              Expanded(
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.w900,
                    color: color,
                    letterSpacing: 1.0,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 2),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.15),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        children: [
          // الأيقونة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Icon(
              icon,
              color: color,
              size: 32,
            ),
          ),
          const SizedBox(height: 16),

          // العنوان
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
              letterSpacing: 0.5,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),

          // القيمة
          Text(
            value,
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.w900,
              color: color,
              letterSpacing: 0.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // بناء قسم أنواع الكروت
  Widget _buildCardTypesSection(Map<String, dynamic> stats) {
    final cardTypes = stats['cardTypes'] as Map<String, int>;
    final totalCards = stats['totalCards'] as int;

    if (cardTypes.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border:
            Border.all(color: const Color(0xFF00695C).withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF00695C).withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF00695C).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.pie_chart,
                  color: Color(0xFF00695C),
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'أنواع الكروت المباعة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF00695C),
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // المخطط الدائري
          SizedBox(
            height: 200,
            child: PieChart(
              PieChartData(
                sections: _buildPieChartSections(cardTypes, totalCards),
                centerSpaceRadius: 40,
                sectionsSpace: 2,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // قائمة أنواع الكروت
          ...cardTypes.entries.map((entry) {
            final percentage =
                (entry.value / totalCards * 100).toStringAsFixed(1);
            return Container(
              margin: const EdgeInsets.symmetric(vertical: 6),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: _getCardTypeColor(entry.key),
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      entry.key,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade800,
                        letterSpacing: 0.3,
                      ),
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFF00695C).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      '${entry.value} ($percentage%)',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF00695C),
                        letterSpacing: 0.3,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  // بناء بطاقة نسبة البيع
  Widget _buildSalesRatioCard(Map<String, dynamic> stats) {
    // هنا يمكن حساب نسبة البيع بناءً على المخزون المتاح
    // للبساطة، سنعرض نسبة مئوية افتراضية
    const salesRatio = 85.5; // يمكن حسابها من البيانات الفعلية

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border:
            Border.all(color: const Color(0xFF00695C).withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF00695C).withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // العنوان
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF00695C).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.trending_up,
                  color: Color(0xFF00695C),
                  size: 28,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'نسبة البيع',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF00695C),
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // النسبة المئوية
          Text(
            '${salesRatio.toStringAsFixed(1)}%',
            style: const TextStyle(
              fontSize: 40,
              fontWeight: FontWeight.w900,
              color: Color(0xFF00695C),
              letterSpacing: 1.0,
            ),
          ),
          const SizedBox(height: 16),

          // شريط التقدم
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: LinearProgressIndicator(
                value: salesRatio / 100,
                backgroundColor: Colors.grey.shade200,
                valueColor:
                    const AlwaysStoppedAnimation<Color>(Color(0xFF00695C)),
                minHeight: 12,
              ),
            ),
          ),
          const SizedBox(height: 12),

          Text(
            'من إجمالي المبيعات المستهدفة',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade700,
              letterSpacing: 0.3,
            ),
          ),
        ],
      ),
    );
  }

  // بناء أقسام المخطط الدائري
  List<PieChartSectionData> _buildPieChartSections(
      Map<String, int> cardTypes, int totalCards) {
    final colors = [
      const Color(0xFF00695C),
      const Color(0xFF004D40),
      const Color(0xFF26A69A),
      const Color(0xFF80CBC4),
      const Color(0xFFB2DFDB),
    ];

    int colorIndex = 0;
    return cardTypes.entries.map((entry) {
      final percentage = entry.value / totalCards * 100;
      final color = colors[colorIndex % colors.length];
      colorIndex++;

      return PieChartSectionData(
        value: entry.value.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        color: color,
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  // الحصول على لون نوع الكارت
  Color _getCardTypeColor(String cardType) {
    final colors = [
      const Color(0xFF00695C),
      const Color(0xFF004D40),
      const Color(0xFF26A69A),
      const Color(0xFF80CBC4),
      const Color(0xFFB2DFDB),
    ];

    return colors[cardType.hashCode % colors.length];
  }

  // تحويل نوع الكارت إلى العربية
  String _convertCardTypeToArabic(String cardType) {
    if (cardType.isEmpty) return 'غير محدد';

    // إذا كان النص عربي بالفعل، أعده كما هو
    if (RegExp(r'[\u0600-\u06FF]').hasMatch(cardType)) {
      return cardType.trim();
    }

    // محاولة الحصول على الاسم من CardTypeProvider
    try {
      final cardTypeProvider = Provider.of<CardTypeProvider>(
        context,
        listen: false,
      );

      // البحث بالمعرف المباشر
      final cardTypeOption = cardTypeProvider.getCardTypeById(cardType);
      if (cardTypeOption != null) {
        return cardTypeOption.displayName;
      }

      // البحث في الأنواع المخصصة
      final customCardType = cardTypeProvider.customCardTypes
          .where((ct) => ct.name == cardType || ct.displayName == cardType)
          .firstOrNull;
      if (customCardType != null) {
        return customCardType.displayName;
      }
    } catch (e) {
      // في حالة الخطأ، نعيد الاسم الأصلي
    }

    // ترجمة احتياطية
    final lowerType = cardType.toLowerCase();
    if (lowerType.contains('zain')) return 'زين';
    if (lowerType.contains('asia')) return 'آسيا';
    if (lowerType.contains('cash')) return 'نقدي';

    return cardType;
  }

  // تنسيق التاريخ الكامل بالأرقام مع اسم اليوم
  String _formatFullDateWithDay(DateTime date) {
    final dayNames = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت'
    ];

    final dayName = dayNames[date.weekday % 7];
    final year = date.year.toString();
    final month = date.month.toString().padLeft(2, '0');
    final day = date.day.toString().padLeft(2, '0');

    return '$dayName $year/$month/$day';
  }

  // تنسيق الوقت فقط
  String _formatTimeOnly(DateTime date) {
    final hour = date.hour;
    final minute = date.minute.toString().padLeft(2, '0');

    if (hour == 0) {
      return '12:$minute ص';
    } else if (hour < 12) {
      return '$hour:$minute ص';
    } else if (hour == 12) {
      return '12:$minute م';
    } else {
      return '${hour - 12}:$minute م';
    }
  }

  // حساب الوقت منذ القيد
  String _getTimeSinceEntry(DateTime entryDate) {
    final now = DateTime.now();
    final difference = now.difference(entryDate);

    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return 'منذ 1 يوم';
      } else {
        return 'منذ ${difference.inDays} أيام';
      }
    } else if (difference.inHours > 0) {
      if (difference.inHours == 1) {
        return 'منذ 1 ساعة';
      } else {
        return 'منذ ${difference.inHours} ساعات';
      }
    } else if (difference.inMinutes > 0) {
      if (difference.inMinutes == 1) {
        return 'منذ 1 دقيقة';
      } else {
        return 'منذ ${difference.inMinutes} دقائق';
      }
    } else {
      return 'الآن';
    }
  }

  // حساب الوقت المتبقي للموعد
  String _getTimeUntilDue(DateTime dueDate) {
    final now = DateTime.now();
    final difference = dueDate.difference(now);

    if (difference.isNegative) {
      final overdueDays = now.difference(dueDate).inDays;
      if (overdueDays == 0) {
        return 'متأخر اليوم';
      } else if (overdueDays == 1) {
        return 'متأخر منذ يوم';
      } else {
        return 'متأخر منذ $overdueDays أيام';
      }
    } else if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return 'يوم واحد';
      } else {
        return '${difference.inDays} أيام';
      }
    } else if (difference.inHours > 0) {
      if (difference.inHours == 1) {
        return 'ساعة واحدة';
      } else {
        return '${difference.inHours} ساعات';
      }
    } else if (difference.inMinutes > 0) {
      if (difference.inMinutes == 1) {
        return 'دقيقة واحدة';
      } else {
        return '${difference.inMinutes} دقائق';
      }
    } else {
      return 'الآن';
    }
  }
}
